#include <catch2/catch_test_macros.hpp>

// C++ standard library
#include <memory>
#include <string>

// Third-party libraries
#define GLFW_INCLUDE_NONE
#include <GLFW/glfw3.h>
#include <glm/glm.hpp>

// Local includes
#include "window/glfw/glfw_window.hpp"
#include "window/glfw/glfw_keyboard.hpp"
#include "window/glfw/glfw_mouse.hpp"
#include "events/events.hpp"
#include "events/event_dispatcher.hpp"
#include "services/service_locator.hpp"

using namespace IronFrost;

// Mock EventDispatcher for testing
class MockEventDispatcher : public EventDispatcher {
private:
    std::vector<std::string> m_dispatchedEvents;
    std::vector<std::pair<float, float>> m_windowResizeEvents;
    int m_windowCloseEventCount = 0;

public:
    void clearEvents() {
        m_dispatchedEvents.clear();
        m_windowResizeEvents.clear();
        m_windowCloseEventCount = 0;
    }

    template<typename T, typename... Args>
    void dispatch(Args&&... args) {
        if constexpr (std::is_same_v<T, WindowResizeEvent>) {
            m_dispatchedEvents.push_back("WindowResizeEvent");
            m_windowResizeEvents.emplace_back(static_cast<float>(args)...);
        } else if constexpr (std::is_same_v<T, WindowCloseEvent>) {
            m_dispatchedEvents.push_back("WindowCloseEvent");
            m_windowCloseEventCount++;
        }

        // Call parent implementation to handle actual event dispatching
        EventDispatcher::dispatch<T>(std::forward<Args>(args)...);
    }

    const std::vector<std::string>& getDispatchedEvents() const {
        return m_dispatchedEvents;
    }

    const std::vector<std::pair<float, float>>& getWindowResizeEvents() const {
        return m_windowResizeEvents;
    }

    int getWindowCloseEventCount() const {
        return m_windowCloseEventCount;
    }

    bool wasEventDispatched(const std::string& eventType) const {
        return std::find(m_dispatchedEvents.begin(), m_dispatchedEvents.end(), eventType) != m_dispatchedEvents.end();
    }
};

// Test fixture for GLFW_Window testing
class GLFWWindowTestFixture {
private:
    MockEventDispatcher* m_mockEventDispatcher;
    std::unique_ptr<GLFW_Window> m_window;

public:
    GLFWWindowTestFixture() {
        // Initialize GLFW if not already initialized
        if (!glfwInit()) {
            throw std::runtime_error("Failed to initialize GLFW for testing");
        }

        // Create mock event dispatcher and register it
        auto mockDispatcher = std::make_unique<MockEventDispatcher>();
        m_mockEventDispatcher = mockDispatcher.get();
        ServiceLocator::registerService<EventDispatcher>(std::move(mockDispatcher));

        // Create window (this will be hidden by default in test environment)
        glfwWindowHint(GLFW_VISIBLE, GLFW_FALSE);
        m_window = GLFW_Window::create(800, 600, "Test Window");

        if (!m_window || !m_window->isInitialized()) {
            throw std::runtime_error("Failed to create GLFW window for testing");
        }
    }

    ~GLFWWindowTestFixture() {
        m_window.reset();
    }

    GLFW_Window& getWindow() {
        return *m_window;
    }

    MockEventDispatcher& getEventDispatcher() {
        return *m_mockEventDispatcher;
    }
};

TEST_CASE("GLFW_Window creation and initialization", "[window][glfw][window][creation]") {
    // Initialize GLFW for this test
    if (!glfwInit()) {
        SKIP("GLFW initialization failed");
    }

    SECTION("Successful window creation") {
        glfwWindowHint(GLFW_VISIBLE, GLFW_FALSE);
        auto window = GLFW_Window::create(800, 600, "Test Window");
        
        REQUIRE(window != nullptr);
        REQUIRE(window->isInitialized() == true);
        REQUIRE(window->getWidth() == 800.0f);
        REQUIRE(window->getHeight() == 600.0f);
    }

    SECTION("Window creation with different dimensions") {
        glfwWindowHint(GLFW_VISIBLE, GLFW_FALSE);
        auto window = GLFW_Window::create(1920, 1080, "Large Window");
        
        REQUIRE(window != nullptr);
        REQUIRE(window->isInitialized() == true);
        REQUIRE(window->getWidth() == 1920.0f);
        REQUIRE(window->getHeight() == 1080.0f);
    }

    SECTION("Window creation with different titles") {
        glfwWindowHint(GLFW_VISIBLE, GLFW_FALSE);
        auto window1 = GLFW_Window::create(640, 480, "Game Window");
        auto window2 = GLFW_Window::create(640, 480, "Editor Window");
        auto window3 = GLFW_Window::create(640, 480, "");
        
        REQUIRE(window1 != nullptr);
        REQUIRE(window2 != nullptr);
        REQUIRE(window3 != nullptr);
        REQUIRE(window1->isInitialized() == true);
        REQUIRE(window2->isInitialized() == true);
        REQUIRE(window3->isInitialized() == true);
    }

    SECTION("Window size properties") {
        glfwWindowHint(GLFW_VISIBLE, GLFW_FALSE);
        auto window = GLFW_Window::create(1024, 768, "Size Test");
        
        REQUIRE(window->getWidth() == 1024.0f);
        REQUIRE(window->getHeight() == 768.0f);
        
        const auto& size = window->getSize();
        REQUIRE(size.x == 1024.0f);
        REQUIRE(size.y == 768.0f);
    }
}

TEST_CASE("GLFW_Window basic functionality", "[window][glfw][window][functionality]") {
    GLFWWindowTestFixture fixture;
    GLFW_Window& window = fixture.getWindow();

    SECTION("Window is properly initialized") {
        REQUIRE(window.isInitialized() == true);
    }

    SECTION("Window should not close initially") {
        REQUIRE(window.shouldClose() == false);
    }

    SECTION("Window dimensions are correct") {
        REQUIRE(window.getWidth() == 800.0f);
        REQUIRE(window.getHeight() == 600.0f);
        
        const auto& size = window.getSize();
        REQUIRE(size.x == 800.0f);
        REQUIRE(size.y == 600.0f);
    }

    SECTION("Raw handle is not null") {
        void* handle = window.getRawHandle();
        REQUIRE(handle != nullptr);
    }

    SECTION("Keyboard and mouse are accessible") {
        IKeyboard& keyboard = window.getKeyboard();
        IMouse& mouse = window.getMouse();
        
        // Should be able to cast to concrete types
        GLFW_Keyboard* glfwKeyboard = dynamic_cast<GLFW_Keyboard*>(&keyboard);
        GLFW_Mouse* glfwMouse = dynamic_cast<GLFW_Mouse*>(&mouse);
        
        REQUIRE(glfwKeyboard != nullptr);
        REQUIRE(glfwMouse != nullptr);
    }
}

TEST_CASE("GLFW_Window event handling", "[window][glfw][window][events]") {
    GLFWWindowTestFixture fixture;
    GLFW_Window& window = fixture.getWindow();

    SECTION("Window close sets shouldClose flag") {
        REQUIRE(window.shouldClose() == false);

        window.close();

        REQUIRE(window.shouldClose() == true);

        // Note: Event dispatching is tested through integration, not mocking
        // since the dispatch method is not virtual
    }

    SECTION("Multiple close calls are safe") {
        REQUIRE(window.shouldClose() == false);

        window.close();
        REQUIRE(window.shouldClose() == true);

        // Multiple close calls should be safe
        window.close();
        window.close();
        REQUIRE(window.shouldClose() == true);
    }
}

TEST_CASE("GLFW_Window lifecycle management", "[window][glfw][window][lifecycle]") {
    SECTION("Window destruction is safe") {
        if (!glfwInit()) {
            SKIP("GLFW initialization failed");
        }

        glfwWindowHint(GLFW_VISIBLE, GLFW_FALSE);
        auto window = GLFW_Window::create(400, 300, "Lifecycle Test");
        REQUIRE(window != nullptr);
        REQUIRE(window->isInitialized() == true);
        
        // Destruction should be handled automatically
        window.reset();
        
        // Should be able to create another window
        window = GLFW_Window::create(400, 300, "Second Window");
        REQUIRE(window != nullptr);
        REQUIRE(window->isInitialized() == true);
    }

    SECTION("Multiple windows can be created and destroyed") {
        if (!glfwInit()) {
            SKIP("GLFW initialization failed");
        }

        glfwWindowHint(GLFW_VISIBLE, GLFW_FALSE);
        
        std::vector<std::unique_ptr<GLFW_Window>> windows;
        
        // Create multiple windows
        for (int i = 0; i < 3; ++i) {
            auto window = GLFW_Window::create(400 + i * 100, 300 + i * 50, "Window " + std::to_string(i));
            REQUIRE(window != nullptr);
            REQUIRE(window->isInitialized() == true);
            windows.push_back(std::move(window));
        }
        
        // All windows should be valid
        for (const auto& window : windows) {
            REQUIRE(window->isInitialized() == true);
            REQUIRE(window->shouldClose() == false);
        }
        
        // Destroy all windows
        windows.clear();
        
        // Should be able to create new windows after destruction
        auto newWindow = GLFW_Window::create(800, 600, "New Window");
        REQUIRE(newWindow != nullptr);
        REQUIRE(newWindow->isInitialized() == true);
    }
}

TEST_CASE("GLFW_Window interface compliance", "[window][glfw][window][interface]") {
    GLFWWindowTestFixture fixture;
    GLFW_Window& window = fixture.getWindow();

    SECTION("IWindow interface methods work correctly") {
        // Test through the interface
        IWindow& iWindow = window;

        // Size methods
        REQUIRE(iWindow.getWidth() == 800.0f);
        REQUIRE(iWindow.getHeight() == 600.0f);

        const auto& size = iWindow.getSize();
        REQUIRE(size.x == 800.0f);
        REQUIRE(size.y == 600.0f);

        // State methods
        REQUIRE(iWindow.shouldClose() == false);

        // Input device access
        IKeyboard& keyboard = iWindow.getKeyboard();
        IMouse& mouse = iWindow.getMouse();

        // References should be valid (no need to check for nullptr)
        REQUIRE(true); // Placeholder to ensure we got valid references

        // Raw handle
        void* handle = iWindow.getRawHandle();
        REQUIRE(handle != nullptr);
    }

    SECTION("Polymorphic behavior works correctly") {
        std::unique_ptr<IWindow> polymorphicWindow = GLFW_Window::create(640, 480, "Polymorphic Test");

        REQUIRE(polymorphicWindow != nullptr);
        REQUIRE(polymorphicWindow->getWidth() == 640.0f);
        REQUIRE(polymorphicWindow->getHeight() == 480.0f);
        REQUIRE(polymorphicWindow->shouldClose() == false);

        // Should be able to access input devices through interface
        IKeyboard& keyboard = polymorphicWindow->getKeyboard();
        IMouse& mouse = polymorphicWindow->getMouse();

        // References should be valid (no need to check for nullptr)
        REQUIRE(true); // Placeholder to ensure we got valid references
    }
}

TEST_CASE("GLFW_Window input device integration", "[window][glfw][window][input]") {
    GLFWWindowTestFixture fixture;
    GLFW_Window& window = fixture.getWindow();

    SECTION("Keyboard integration") {
        IKeyboard& keyboard = window.getKeyboard();
        GLFW_Keyboard& glfwKeyboard = dynamic_cast<GLFW_Keyboard&>(keyboard);

        // Test basic keyboard functionality
        REQUIRE(keyboard.isKeyUp(KEY_A) == true);
        REQUIRE(keyboard.isKeyDown(KEY_A) == false);

        // Test through concrete class
        glfwKeyboard.setKeyDown(KEY_A);
        REQUIRE(keyboard.isKeyDown(KEY_A) == true);
        REQUIRE(keyboard.isKeyUp(KEY_A) == false);

        glfwKeyboard.setKeyUp(KEY_A);
        REQUIRE(keyboard.isKeyUp(KEY_A) == true);
        REQUIRE(keyboard.isKeyDown(KEY_A) == false);
    }

    SECTION("Mouse integration") {
        IMouse& mouse = window.getMouse();
        GLFW_Mouse& glfwMouse = dynamic_cast<GLFW_Mouse&>(mouse);

        // Test basic mouse functionality
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == false);

        // Test position access
        REQUIRE(mouse.getXPos() == 0.0);
        REQUIRE(mouse.getYPos() == 0.0);

        // Test through concrete class
        glfwMouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == false);

        glfwMouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_UP);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == false);
    }

    SECTION("Input devices are persistent") {
        IKeyboard& keyboard1 = window.getKeyboard();
        IKeyboard& keyboard2 = window.getKeyboard();
        IMouse& mouse1 = window.getMouse();
        IMouse& mouse2 = window.getMouse();

        // Should return the same instances
        REQUIRE(&keyboard1 == &keyboard2);
        REQUIRE(&mouse1 == &mouse2);
    }
}

TEST_CASE("GLFW_Window error handling and edge cases", "[window][glfw][window][edge_cases]") {
    SECTION("Window creation failure handling") {
        // Note: GLFW_Window::create calls glfwInit() internally, so terminating GLFW
        // doesn't actually cause failure. This test verifies the behavior when
        // window creation succeeds despite GLFW state.

        auto window = GLFW_Window::create(800, 600, "Test Window");

        // Window creation should succeed because create() calls glfwInit()
        if (window) {
            REQUIRE(window->isInitialized() == true);
        }
        // If it fails, that's also acceptable behavior
    }

    SECTION("Extreme window dimensions") {
        if (!glfwInit()) {
            SKIP("GLFW initialization failed");
        }

        glfwWindowHint(GLFW_VISIBLE, GLFW_FALSE);

        // Very small window
        auto smallWindow = GLFW_Window::create(1, 1, "Tiny Window");
        if (smallWindow) {
            REQUIRE(smallWindow->isInitialized() == true);
            REQUIRE(smallWindow->getWidth() == 1.0f);
            REQUIRE(smallWindow->getHeight() == 1.0f);
        }

        // Large window (within reasonable limits)
        auto largeWindow = GLFW_Window::create(3840, 2160, "Large Window");
        if (largeWindow) {
            REQUIRE(largeWindow->isInitialized() == true);
            REQUIRE(largeWindow->getWidth() == 3840.0f);
            REQUIRE(largeWindow->getHeight() == 2160.0f);
        }
    }

    SECTION("Special characters in window title") {
        if (!glfwInit()) {
            SKIP("GLFW initialization failed");
        }

        glfwWindowHint(GLFW_VISIBLE, GLFW_FALSE);

        // Test various special characters and Unicode
        std::vector<std::string> titles = {
            "Window with spaces",
            "Window-with-dashes",
            "Window_with_underscores",
            "Window.with.dots",
            "Window@#$%^&*()",
            "Window[with]brackets",
            "Window{with}braces",
            "Window/with\\slashes",
            "Окно", // Cyrillic
            "窓", // Japanese
            "🎮 Game Window 🎮" // Emoji
        };

        for (const auto& title : titles) {
            auto window = GLFW_Window::create(400, 300, title);
            if (window) {
                REQUIRE(window->isInitialized() == true);
            }
        }
    }
}

TEST_CASE("GLFW_Window operations", "[window][glfw][window][operations]") {
    GLFWWindowTestFixture fixture;
    GLFW_Window& window = fixture.getWindow();

    SECTION("Window polling operations") {
        // These operations should not crash
        REQUIRE_NOTHROW(window.pollEvents());
        REQUIRE_NOTHROW(window.pollEvents());
        REQUIRE_NOTHROW(window.pollEvents());
    }

    SECTION("Buffer swapping operations") {
        // These operations should not crash
        REQUIRE_NOTHROW(window.swapBuffers());
        REQUIRE_NOTHROW(window.swapBuffers());
        REQUIRE_NOTHROW(window.swapBuffers());
    }

    SECTION("Combined operations") {
        // Simulate typical game loop operations
        for (int i = 0; i < 10; ++i) {
            REQUIRE_NOTHROW(window.pollEvents());
            REQUIRE(window.shouldClose() == false);
            REQUIRE_NOTHROW(window.swapBuffers());
        }
    }

    SECTION("Operations after close") {
        window.close();
        REQUIRE(window.shouldClose() == true);

        // Operations should still be safe after close
        REQUIRE_NOTHROW(window.pollEvents());
        REQUIRE_NOTHROW(window.swapBuffers());

        // Multiple close calls should be safe
        REQUIRE_NOTHROW(window.close());
        REQUIRE_NOTHROW(window.close());
    }
}

TEST_CASE("GLFW_Window performance characteristics", "[window][glfw][window][performance]") {
    GLFWWindowTestFixture fixture;
    GLFW_Window& window = fixture.getWindow();

    SECTION("Rapid state queries") {
        // Rapid queries should be consistent and fast
        for (int i = 0; i < 10000; ++i) {
            REQUIRE(window.isInitialized() == true);
            REQUIRE(window.getWidth() == 800.0f);
            REQUIRE(window.getHeight() == 600.0f);

            const auto& size = window.getSize();
            REQUIRE(size.x == 800.0f);
            REQUIRE(size.y == 600.0f);
        }
    }

    SECTION("Rapid input device access") {
        // Rapid access to input devices should be consistent
        IKeyboard* firstKeyboard = nullptr;
        IMouse* firstMouse = nullptr;

        for (int i = 0; i < 1000; ++i) {
            IKeyboard& keyboard = window.getKeyboard();
            IMouse& mouse = window.getMouse();

            if (i == 0) {
                firstKeyboard = &keyboard;
                firstMouse = &mouse;
            } else {
                // Should return the same instances
                REQUIRE(&keyboard == firstKeyboard);
                REQUIRE(&mouse == firstMouse);
            }
        }
    }

    SECTION("Rapid operations") {
        // Rapid window operations should be safe
        for (int i = 0; i < 100; ++i) {
            REQUIRE_NOTHROW(window.pollEvents());
            REQUIRE(window.shouldClose() == false);
            REQUIRE_NOTHROW(window.swapBuffers());

            void* handle = window.getRawHandle();
            REQUIRE(handle != nullptr);
        }
    }
}

TEST_CASE("GLFW_Window static factory method", "[window][glfw][window][factory]") {
    SECTION("Factory method creates valid windows") {
        if (!glfwInit()) {
            SKIP("GLFW initialization failed");
        }

        glfwWindowHint(GLFW_VISIBLE, GLFW_FALSE);

        auto window = GLFW_Window::create(1024, 768, "Factory Test");

        REQUIRE(window != nullptr);
        REQUIRE(window->isInitialized() == true);
        REQUIRE(window->getWidth() == 1024.0f);
        REQUIRE(window->getHeight() == 768.0f);
        REQUIRE(window->shouldClose() == false);
    }

    SECTION("Factory method handles different parameters") {
        if (!glfwInit()) {
            SKIP("GLFW initialization failed");
        }

        glfwWindowHint(GLFW_VISIBLE, GLFW_FALSE);

        struct TestCase {
            int width;
            int height;
            std::string title;
        };

        std::vector<TestCase> testCases = {
            {800, 600, "Standard"},
            {1920, 1080, "HD"},
            {640, 480, "VGA"},
            {1366, 768, "Laptop"},
            {3840, 2160, "4K"},
            {400, 300, "Small"},
            {100, 100, "Tiny"}
        };

        for (const auto& testCase : testCases) {
            auto window = GLFW_Window::create(testCase.width, testCase.height, testCase.title);

            if (window) {
                REQUIRE(window->isInitialized() == true);
                REQUIRE(window->getWidth() == static_cast<float>(testCase.width));
                REQUIRE(window->getHeight() == static_cast<float>(testCase.height));
                REQUIRE(window->shouldClose() == false);
            }
        }
    }

    SECTION("Factory method handles initialization") {
        // The factory method handles GLFW initialization internally
        auto window = GLFW_Window::create(800, 600, "Factory Window");

        // Should succeed because create() handles initialization
        if (window) {
            REQUIRE(window->isInitialized() == true);
        }
        // If it fails, that's also acceptable behavior depending on system state
    }
}

TEST_CASE("GLFW_Window real-world usage scenarios", "[window][glfw][window][real_world]") {
    GLFWWindowTestFixture fixture;
    GLFW_Window& window = fixture.getWindow();

    SECTION("Game loop simulation") {
        // Simulate a typical game loop
        for (int frame = 0; frame < 60; ++frame) {
            // Poll events
            window.pollEvents();

            // Check if window should close
            if (window.shouldClose()) {
                break;
            }

            // Access input devices
            IKeyboard& keyboard = window.getKeyboard();
            IMouse& mouse = window.getMouse();

            // Simulate some input checking
            bool keyPressed = keyboard.isKeyDown(KEY_SPACE);
            bool mousePressed = mouse.isButtonPressed(MOUSE_BUTTON_LEFT);

            // Swap buffers
            window.swapBuffers();
        }

        // Should complete without issues
        REQUIRE(window.isInitialized() == true);
    }

    SECTION("Window lifecycle in application") {
        // Application startup
        REQUIRE(window.isInitialized() == true);
        REQUIRE(window.shouldClose() == false);

        // Application running
        for (int i = 0; i < 10; ++i) {
            window.pollEvents();
            window.swapBuffers();
        }

        // User requests close
        window.close();
        REQUIRE(window.shouldClose() == true);

        // Application cleanup (still safe to call operations)
        window.pollEvents();
        window.swapBuffers();
    }

    SECTION("Input handling integration") {
        IKeyboard& keyboard = window.getKeyboard();
        IMouse& mouse = window.getMouse();
        GLFW_Keyboard& glfwKeyboard = dynamic_cast<GLFW_Keyboard&>(keyboard);
        GLFW_Mouse& glfwMouse = dynamic_cast<GLFW_Mouse&>(mouse);

        // Simulate user input
        glfwKeyboard.setKeyDown(KEY_W);
        glfwKeyboard.setKeyDown(KEY_A);
        glfwMouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_DOWN);

        // Check input state
        REQUIRE(keyboard.isKeyDown(KEY_W) == true);
        REQUIRE(keyboard.isKeyDown(KEY_A) == true);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);

        // Process events
        window.pollEvents();

        // Input should still be active
        REQUIRE(keyboard.isKeyDown(KEY_W) == true);
        REQUIRE(keyboard.isKeyDown(KEY_A) == true);
        REQUIRE(mouse.isButtonPressed(MOUSE_BUTTON_LEFT) == true);

        // Release input
        glfwKeyboard.setKeyUp(KEY_W);
        glfwKeyboard.setKeyUp(KEY_A);
        glfwMouse.updateButtonState(MOUSE_BUTTON_LEFT, MOUSE_BUTTON_UP);

        // Check released state
        REQUIRE(keyboard.isKeyUp(KEY_W) == true);
        REQUIRE(keyboard.isKeyUp(KEY_A) == true);
        REQUIRE(mouse.isButtonReleased(MOUSE_BUTTON_LEFT) == true);
    }
}

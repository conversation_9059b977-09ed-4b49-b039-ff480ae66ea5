#include <catch2/catch_test_macros.hpp>

// C++ standard library
#include <memory>
#include <string>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "gui/widgets/label_widget.hpp"
#include "utils/string_id.hpp"
#include "../../test_utils.hpp"
#include "../../mocks/test_mocks.hpp"

using namespace IronFrost;

TEST_CASE("LabelWidget basic functionality", "[gui][widgets][label_widget][basic]") {
    SECTION("Constructor with font and text") {
        StringID font("font::arial::16");
        std::string text = "Hello World";
        
        LabelWidget label(font, text);
        
        REQUIRE(label.getFontName() == font);
        REQUIRE(label.getText() == text);
        REQUIRE(label.getType() == "label");

        // Use shared utilities for common property verification (CLEANUP)
        VERIFY_DEFAULT_WIDGET_STATE(label);
        VERIFY_DEFAULT_COLORABLE_PROPERTIES(label);
    }
    
    SECTION("Constructor with position, size, font, and text") {
        glm::vec2 position(100.0f, 200.0f);
        glm::vec2 size(300.0f, 50.0f);
        StringID font("font::roboto::24");
        std::string text = "Test Label";
        
        LabelWidget label(position, size, font, text);
        
        REQUIRE(label.getPosition() == position);
        REQUIRE(label.getSize() == size);
        REQUIRE(label.getFontName() == font);
        REQUIRE(label.getText() == text);
        REQUIRE(label.getType() == "label");
    }
}

TEST_CASE("LabelWidget text management", "[gui][widgets][label_widget][text]") {
    SECTION("Set and get text") {
        StringID font("font::test");
        LabelWidget label(font, "Initial Text");
        
        std::string newText = "Updated Text";
        label.setText(newText);
        
        REQUIRE(label.getText() == newText);
        REQUIRE(label.isDirty() == true); // Should mark dirty when text changes
    }
    
    SECTION("Setting text marks widget as dirty") {
        StringID font("font::test");
        LabelWidget label(font, "Original");
        label.markClean();
        REQUIRE(label.isDirty() == false);
        
        label.setText("Modified");
        REQUIRE(label.isDirty() == true);
    }
    
    SECTION("Empty text") {
        StringID font("font::test");
        LabelWidget label(font, "");
        
        REQUIRE(label.getText() == "");
        
        label.setText("Some text");
        REQUIRE(label.getText() == "Some text");
        
        label.setText("");
        REQUIRE(label.getText() == "");
    }
    
    SECTION("Long text") {
        StringID font("font::test");
        std::string longText(1000, 'A');
        
        LabelWidget label(font, longText);
        REQUIRE(label.getText() == longText);
        
        std::string evenLongerText(5000, 'B');
        label.setText(evenLongerText);
        REQUIRE(label.getText() == evenLongerText);
    }
    
    SECTION("Text with special characters") {
        StringID font("font::test");
        LabelWidget label(font, "");
        
        std::vector<std::string> specialTexts = {
            "Hello\nWorld",           // Newline
            "Tab\tSeparated",         // Tab
            "Quote\"Test\"",          // Quotes
            "Apostrophe's Test",      // Apostrophe
            "Numbers: 12345",         // Numbers
            "Symbols: !@#$%^&*()",    // Symbols
            "Unicode: тест",          // Cyrillic
            "Emoji: 🎮🚀",           // Emoji
            "Mixed: Hello\n\tWorld!"  // Mixed special chars
        };
        
        for (const auto& text : specialTexts) {
            label.setText(text);
            REQUIRE(label.getText() == text);
            REQUIRE(label.isDirty() == true);
            label.markClean();
        }
    }
    
    SECTION("Multiline text") {
        StringID font("font::test");
        std::string multilineText = "Line 1\nLine 2\nLine 3\nLine 4";
        
        LabelWidget label(font, multilineText);
        REQUIRE(label.getText() == multilineText);
        
        // Count newlines
        size_t newlineCount = 0;
        for (char c : label.getText()) {
            if (c == '\n') newlineCount++;
        }
        REQUIRE(newlineCount == 3);
    }
}

TEST_CASE("LabelWidget font management", "[gui][widgets][label_widget][font]") {
    SECTION("Set and get font name") {
        StringID initialFont("font::arial::12");
        LabelWidget label(initialFont, "Test");
        
        StringID newFont("font::helvetica::16");
        label.setFontName(newFont);
        
        REQUIRE(label.getFontName() == newFont);
        REQUIRE(label.isDirty() == true); // Should mark dirty when font changes
    }
    
    SECTION("Setting font marks widget as dirty") {
        StringID font1("font::test1");
        StringID font2("font::test2");
        LabelWidget label(font1, "Test");
        
        label.markClean();
        REQUIRE(label.isDirty() == false);
        
        label.setFontName(font2);
        REQUIRE(label.isDirty() == true);
    }
    
    SECTION("Multiple font changes") {
        StringID initialFont("font::initial");
        LabelWidget label(initialFont, "Test");
        
        std::vector<StringID> fonts = {
            StringID("font::arial::12"),
            StringID("font::helvetica::16"),
            StringID("font::times::20"),
            StringID("font::courier::14"),
            StringID("font::roboto::18")
        };
        
        for (const auto& font : fonts) {
            label.setFontName(font);
            REQUIRE(label.getFontName() == font);
            REQUIRE(label.isDirty() == true);
            label.markClean();
        }
    }
    
    SECTION("Font names with special characters") {
        StringID initialFont("font::test");
        LabelWidget label(initialFont, "Test");
        
        std::vector<std::string> specialFontNames = {
            "font::arial-bold::16",
            "font::helvetica_light::14",
            "font::times.new.roman::18",
            "font::courier@2x::12",
            "font::roboto#condensed::20",
            "font::font with spaces::16",
            "font::шрифт::14", // Unicode
            "font::🎨::16"     // Emoji
        };
        
        for (const auto& fontName : specialFontNames) {
            StringID font(fontName);
            label.setFontName(font);
            REQUIRE(label.getFontName() == font);
        }
    }
}

TEST_CASE("LabelWidget inheritance functionality", "[gui][widgets][label_widget][inheritance]") {
    SECTION("Widget functionality - visibility and interaction") {
        StringID font("font::test");
        LabelWidget label(glm::vec2(50.0f, 50.0f), glm::vec2(200.0f, 30.0f), font, "Click Me");
        
        MockKeyboard mockKeyboard;
        MockMouse mockMouse;
        GameContext context = createTestGameContext(mockKeyboard, mockMouse);
        
        // Test visibility
        REQUIRE(label.isVisible() == true);
        label.setVisible(false);
        REQUIRE(label.isVisible() == false);
        
        // Test interaction when invisible
        mockMouse.setPosition(150.0f, 65.0f); // Inside bounds
        mockMouse.setButtonPressed(MOUSE_BUTTON_LEFT, true);
        label.update(0.016f, context);
        
        REQUIRE_FALSE(label.isHovered()); // Should not hover when invisible
        REQUIRE_FALSE(label.isPressed()); // Should not press when invisible
        
        // Test interaction when visible
        label.setVisible(true);
        label.update(0.016f, context);
        
        REQUIRE(label.isHovered()); // Should hover when visible and mouse inside
        REQUIRE(label.isPressed()); // Should press when visible and mouse pressed
    }
    
    SECTION("Transformable functionality - position and size") {
        StringID font("font::test");
        LabelWidget label(font, "Transform Test");
        
        // Test position changes
        glm::vec2 newPosition(300.0f, 400.0f);
        label.setPosition(newPosition);
        REQUIRE(label.getPosition() == newPosition);
        REQUIRE(label.isDirty() == true);
        
        label.markClean();
        
        // Test size changes
        glm::vec2 newSize(400.0f, 60.0f);
        label.setSize(newSize);
        REQUIRE(label.getSize() == newSize);
        REQUIRE(label.isDirty() == true);
        
        label.markClean();
        
        // Test rotation changes
        float newRotation = 15.0f;
        label.setRotation(newRotation);
        REQUIRE(label.getRotation() == newRotation);
        REQUIRE(label.isDirty() == true);
    }
    
    SECTION("Colorable functionality - color and alpha") {
        StringID font("font::test");
        LabelWidget label(font, "Color Test");
        
        // Test color changes
        glm::vec3 newColor(0.2f, 0.8f, 0.4f);
        label.setColor(newColor);
        
        const auto& color = label.getColor();
        REQUIRE(color.r == 0.2f);
        REQUIRE(color.g == 0.8f);
        REQUIRE(color.b == 0.4f);
        
        // Test alpha changes
        float newAlpha = 0.6f;
        label.setAlpha(newAlpha);
        REQUIRE(label.getAlpha() == newAlpha);
        
        // Color should remain unchanged when alpha changes
        const auto& colorAfterAlpha = label.getColor();
        REQUIRE(colorAfterAlpha.r == 0.2f);
        REQUIRE(colorAfterAlpha.g == 0.8f);
        REQUIRE(colorAfterAlpha.b == 0.4f);
    }
    
    SECTION("Callbackable functionality - callbacks") {
        StringID font("font::test");
        LabelWidget label(font, "Callback Test");
        
        bool hoverTriggered = false;
        bool clickTriggered = false;
        
        label.setCallback("onHover", [&hoverTriggered]() {
            hoverTriggered = true;
        });
        
        label.setCallback("onClick", [&clickTriggered]() {
            clickTriggered = true;
        });
        
        // Manually trigger callbacks to test integration
        label.triggerCallback("onHover");
        label.triggerCallback("onClick");
        
        REQUIRE(hoverTriggered);
        REQUIRE(clickTriggered);
    }
}

TEST_CASE("LabelWidget edge cases", "[gui][widgets][label_widget][edge_cases]") {
    SECTION("Empty font name") {
        StringID emptyFont("");
        LabelWidget label(emptyFont, "Test");
        
        REQUIRE(label.getFontName() == emptyFont);
        REQUIRE(label.getText() == "Test");
    }
    
    SECTION("Very long font name") {
        std::string longFontName(1000, 'f');
        StringID longFont(longFontName);
        LabelWidget label(longFont, "Test");
        
        REQUIRE(label.getFontName() == longFont);
    }
    
    SECTION("Zero size label widget") {
        StringID font("font::test");
        glm::vec2 zeroSize(0.0f, 0.0f);
        LabelWidget label(glm::vec2(100.0f, 100.0f), zeroSize, font, "Zero Size");
        
        REQUIRE(label.getSize() == zeroSize);
        REQUIRE(label.getText() == "Zero Size");
        
        MockKeyboard mockKeyboard;
        MockMouse mockMouse;
        GameContext context = createTestGameContext(mockKeyboard, mockMouse);
        
        // Mouse at the exact position should not trigger hover (size is 0)
        mockMouse.setPosition(100.0f, 100.0f);
        label.update(0.016f, context);
        
        REQUIRE_FALSE(label.isHovered());
    }
    
    SECTION("Negative position") {
        StringID font("font::test");
        glm::vec2 negativePosition(-100.0f, -50.0f);
        LabelWidget label(negativePosition, glm::vec2(200.0f, 30.0f), font, "Negative Pos");
        
        REQUIRE(label.getPosition() == negativePosition);
        REQUIRE(label.getText() == "Negative Pos");
    }
    
    SECTION("Large position and size values") {
        StringID font("font::test");
        glm::vec2 largePosition(1e6f, 1e6f);
        glm::vec2 largeSize(1e5f, 1e4f);
        LabelWidget label(largePosition, largeSize, font, "Large Values");
        
        REQUIRE(label.getPosition() == largePosition);
        REQUIRE(label.getSize() == largeSize);
        REQUIRE(label.getText() == "Large Values");
    }
}

TEST_CASE("LabelWidget real-world scenarios", "[gui][widgets][label_widget][real_world]") {
    SECTION("UI button label with state changes") {
        StringID font("font::ui::button");
        LabelWidget buttonLabel(glm::vec2(100.0f, 50.0f), glm::vec2(120.0f, 40.0f), font, "Start Game");

        // Normal state
        REQUIRE(buttonLabel.getText() == "Start Game");
        REQUIRE(buttonLabel.getColor() == glm::vec3(1.0f, 1.0f, 1.0f));
        REQUIRE(buttonLabel.getAlpha() == 1.0f);

        // Hover state - change color
        buttonLabel.setColor(glm::vec3(1.2f, 1.2f, 1.0f)); // Slightly yellow tint
        REQUIRE(buttonLabel.getColor() == glm::vec3(1.2f, 1.2f, 1.0f));

        // Pressed state - change color and text
        buttonLabel.setText("Starting...");
        buttonLabel.setColor(glm::vec3(0.8f, 0.8f, 0.8f)); // Darker

        REQUIRE(buttonLabel.getText() == "Starting...");
        REQUIRE(buttonLabel.getColor() == glm::vec3(0.8f, 0.8f, 0.8f));

        // Disabled state - change text, color, and alpha
        buttonLabel.setText("Unavailable");
        buttonLabel.setColor(glm::vec3(0.5f, 0.5f, 0.5f)); // Grayed out
        buttonLabel.setAlpha(0.5f); // Semi-transparent

        REQUIRE(buttonLabel.getText() == "Unavailable");
        REQUIRE(buttonLabel.getColor() == glm::vec3(0.5f, 0.5f, 0.5f));
        REQUIRE(buttonLabel.getAlpha() == 0.5f);
    }

    SECTION("Game HUD score display") {
        StringID font("font::hud::score");
        LabelWidget scoreLabel(glm::vec2(10.0f, 10.0f), glm::vec2(200.0f, 30.0f), font, "Score: 0");

        // Simulate score updates
        std::vector<int> scores = {0, 100, 250, 1000, 5000, 12345, 999999};

        for (int score : scores) {
            std::string scoreText = "Score: " + std::to_string(score);
            scoreLabel.setText(scoreText);

            REQUIRE(scoreLabel.getText() == scoreText);
            REQUIRE(scoreLabel.isDirty() == true); // Each update should mark dirty
            scoreLabel.markClean();
        }

        // High score achievement - change color
        scoreLabel.setText("NEW HIGH SCORE!");
        scoreLabel.setColor(glm::vec3(1.0f, 0.8f, 0.0f)); // Gold color

        REQUIRE(scoreLabel.getText() == "NEW HIGH SCORE!");
        REQUIRE(scoreLabel.getColor() == glm::vec3(1.0f, 0.8f, 0.0f));
    }

    SECTION("Dialog text with line breaks") {
        StringID font("font::dialog");
        std::string dialogText = "Welcome to the game!\nPress SPACE to continue.\n\nGood luck, adventurer!";

        LabelWidget dialogLabel(glm::vec2(50.0f, 100.0f), glm::vec2(400.0f, 120.0f), font, dialogText);

        REQUIRE(dialogLabel.getText() == dialogText);

        // Count lines
        size_t lineCount = 1; // Start with 1 for the first line
        for (char c : dialogLabel.getText()) {
            if (c == '\n') lineCount++;
        }
        REQUIRE(lineCount == 4); // Should have 4 lines

        // Update dialog
        std::string newDialog = "Chapter 1: The Beginning\n\nYou find yourself in a dark forest.\nWhat do you do?\n\n1. Go north\n2. Go south\n3. Rest";
        dialogLabel.setText(newDialog);

        REQUIRE(dialogLabel.getText() == newDialog);
    }

    SECTION("Interactive label with click handling") {
        StringID font("font::ui::interactive");
        LabelWidget interactiveLabel(glm::vec2(200.0f, 300.0f), glm::vec2(150.0f, 25.0f), font, "Click me!");

        bool wasClicked = false;
        bool wasHovered = false;
        int clickCount = 0;

        interactiveLabel.setCallback("onClick", [&wasClicked, &clickCount]() {
            wasClicked = true;
            clickCount++;
        });

        interactiveLabel.setCallback("onHover", [&wasHovered]() {
            wasHovered = true;
        });

        MockKeyboard mockKeyboard;
        MockMouse mockMouse;
        GameContext context = createTestGameContext(mockKeyboard, mockMouse);

        // Mouse enters label area
        mockMouse.setPosition(275.0f, 312.0f); // Center of label
        interactiveLabel.update(0.016f, context);

        REQUIRE(interactiveLabel.isHovered());
        REQUIRE(wasHovered);

        // Mouse clicks
        mockMouse.setButtonPressed(MOUSE_BUTTON_LEFT, true);
        interactiveLabel.update(0.016f, context);

        REQUIRE(interactiveLabel.isPressed());
        REQUIRE(wasClicked);
        REQUIRE(clickCount == 1);

        // Update text on click
        interactiveLabel.setText("Clicked!");
        REQUIRE(interactiveLabel.getText() == "Clicked!");
    }

    SECTION("Localization text changes") {
        StringID font("font::ui::localized");
        LabelWidget localizedLabel(glm::vec2(100.0f, 200.0f), glm::vec2(200.0f, 30.0f), font, "Hello");

        // Simulate different languages
        struct LocalizedText {
            std::string language;
            std::string text;
        };

        std::vector<LocalizedText> localizations = {
            {"English", "Hello"},
            {"Spanish", "Hola"},
            {"French", "Bonjour"},
            {"German", "Hallo"},
            {"Italian", "Ciao"},
            {"Portuguese", "Olá"},
            {"Russian", "Привет"},
            {"Japanese", "こんにちは"},
            {"Chinese", "你好"},
            {"Arabic", "مرحبا"}
        };

        for (const auto& loc : localizations) {
            localizedLabel.setText(loc.text);
            REQUIRE(localizedLabel.getText() == loc.text);
            REQUIRE(localizedLabel.isDirty() == true);
            localizedLabel.markClean();
        }
    }

    SECTION("Dynamic font sizing for different screen resolutions") {
        LabelWidget responsiveLabel(glm::vec2(0.0f, 0.0f), glm::vec2(300.0f, 50.0f), StringID("font::base"), "Responsive Text");

        // Simulate different screen resolutions with appropriate fonts
        struct Resolution {
            glm::vec2 screenSize;
            StringID font;
            glm::vec2 labelSize;
        };

        std::vector<Resolution> resolutions = {
            {{1280.0f, 720.0f}, StringID("font::small::12"), {200.0f, 30.0f}},   // 720p
            {{1920.0f, 1080.0f}, StringID("font::medium::16"), {300.0f, 50.0f}}, // 1080p
            {{2560.0f, 1440.0f}, StringID("font::large::20"), {400.0f, 60.0f}},  // 1440p
            {{3840.0f, 2160.0f}, StringID("font::xlarge::32"), {600.0f, 100.0f}} // 4K
        };

        for (const auto& res : resolutions) {
            responsiveLabel.setFontName(res.font);
            responsiveLabel.setSize(res.labelSize);

            REQUIRE(responsiveLabel.getFontName() == res.font);
            REQUIRE(responsiveLabel.getSize() == res.labelSize);
            REQUIRE(responsiveLabel.getText() == "Responsive Text");
        }
    }

    SECTION("Label with color animation") {
        StringID font("font::animated");
        LabelWidget animatedLabel(glm::vec2(150.0f, 250.0f), glm::vec2(200.0f, 40.0f), font, "Animated Text");

        // Simulate color animation frames
        std::vector<glm::vec3> colorFrames = {
            {1.0f, 0.0f, 0.0f}, // Red
            {1.0f, 0.5f, 0.0f}, // Orange
            {1.0f, 1.0f, 0.0f}, // Yellow
            {0.0f, 1.0f, 0.0f}, // Green
            {0.0f, 0.0f, 1.0f}, // Blue
            {0.5f, 0.0f, 1.0f}, // Purple
            {1.0f, 0.0f, 1.0f}  // Magenta
        };

        for (const auto& color : colorFrames) {
            animatedLabel.setColor(color);

            const auto& currentColor = animatedLabel.getColor();
            REQUIRE(currentColor.r == color.r);
            REQUIRE(currentColor.g == color.g);
            REQUIRE(currentColor.b == color.b);

            // Text should remain unchanged
            REQUIRE(animatedLabel.getText() == "Animated Text");
        }

        // Simulate alpha fade
        std::vector<float> alphaFrames = {1.0f, 0.8f, 0.6f, 0.4f, 0.2f, 0.0f, 0.2f, 0.4f, 0.6f, 0.8f, 1.0f};

        for (float alpha : alphaFrames) {
            animatedLabel.setAlpha(alpha);
            REQUIRE(animatedLabel.getAlpha() == alpha);
        }
    }

    SECTION("Form label with validation states") {
        StringID font("font::form");
        LabelWidget formLabel(glm::vec2(50.0f, 150.0f), glm::vec2(100.0f, 20.0f), font, "Username:");

        // Normal state
        REQUIRE(formLabel.getText() == "Username:");
        REQUIRE(formLabel.getColor() == glm::vec3(1.0f, 1.0f, 1.0f));

        // Error state
        formLabel.setText("Username: (required)");
        formLabel.setColor(glm::vec3(1.0f, 0.3f, 0.3f)); // Red

        REQUIRE(formLabel.getText() == "Username: (required)");
        REQUIRE(formLabel.getColor() == glm::vec3(1.0f, 0.3f, 0.3f));

        // Success state
        formLabel.setText("Username: ✓");
        formLabel.setColor(glm::vec3(0.3f, 1.0f, 0.3f)); // Green

        REQUIRE(formLabel.getText() == "Username: ✓");
        REQUIRE(formLabel.getColor() == glm::vec3(0.3f, 1.0f, 0.3f));

        // Warning state
        formLabel.setText("Username: (too short)");
        formLabel.setColor(glm::vec3(1.0f, 0.8f, 0.0f)); // Yellow/Orange

        REQUIRE(formLabel.getText() == "Username: (too short)");
        REQUIRE(formLabel.getColor() == glm::vec3(1.0f, 0.8f, 0.0f));
    }
}

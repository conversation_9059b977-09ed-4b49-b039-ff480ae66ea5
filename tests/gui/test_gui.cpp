#include <catch2/catch_test_macros.hpp>
#include <catch2/matchers/catch_matchers_string.hpp>

// C++ standard library
#include <memory>
#include <string>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "gui/gui.hpp"
#include "gui/widgets/label_widget.hpp"
#include "gui/widgets/image_widget.hpp"
#include "gui/widgets/panel_widget.hpp"
#include "utils/string_id.hpp"
#include "../test_utils.hpp"
#include "../mocks/test_mocks.hpp"

using namespace IronFrost;

TEST_CASE("GUI basic functionality", "[gui][gui_class][basic]") {
    SECTION("Default constructor") {
        GUI gui;
        
        // GUI should be empty initially
        // No direct way to check size, but we can test that getting a non-existent widget throws
        REQUIRE_THROWS_AS(gui.getWidget(StringID("nonexistent")), std::runtime_error);
    }
    
    SECTION("Copy constructor is deleted") {
        // This should not compile - testing that copy constructor is deleted
        // GUI gui1;
        // GUI gui2 = gui1; // Should not compile
        
        // We can't directly test deleted constructors in runtime tests,
        // but the compilation will fail if someone tries to use them
        REQUIRE(true); // Placeholder to indicate this section exists
    }
}

TEST_CASE("GUI widget management", "[gui][gui_class][widgets]") {
    SECTION("Add widget with unique_ptr") {
        GUI gui;
        
        StringID widgetId("test_widget");
        auto widget = std::make_unique<Widget>(glm::vec2(10.0f, 20.0f), glm::vec2(50.0f, 30.0f));
        
        gui.addWidget(widgetId, std::move(widget));
        
        // Retrieve and verify the widget
        Widget& retrievedWidget = gui.getWidget(widgetId);
        REQUIRE(retrievedWidget.getPosition() == glm::vec2(10.0f, 20.0f));
        REQUIRE(retrievedWidget.getSize() == glm::vec2(50.0f, 30.0f));
        REQUIRE(retrievedWidget.getType() == "widget");
    }
    
    SECTION("Add widget with raw pointer") {
        GUI gui;
        
        StringID widgetId("raw_widget");
        Widget* rawWidget = new Widget(glm::vec2(15.0f, 25.0f), glm::vec2(60.0f, 40.0f));
        
        gui.addWidgetRaw(widgetId, rawWidget);
        
        // Retrieve and verify the widget
        Widget& retrievedWidget = gui.getWidget(widgetId);
        REQUIRE(retrievedWidget.getPosition() == glm::vec2(15.0f, 25.0f));
        REQUIRE(retrievedWidget.getSize() == glm::vec2(60.0f, 40.0f));
        REQUIRE(retrievedWidget.getType() == "widget");
    }
    
    SECTION("Add multiple widgets of different types") {
        GUI gui;
        
        // Add different types of widgets
        StringID labelId("label");
        StringID imageId("image");
        StringID panelId("panel");
        StringID widgetId("widget");
        
        auto label = std::make_unique<LabelWidget>(StringID("font::test"), "Test Label");
        auto image = std::make_unique<ImageWidget>(StringID("texture::test"));
        auto panel = std::make_unique<PanelWidget>(glm::vec2(0.0f, 0.0f), glm::vec2(200.0f, 150.0f));
        auto widget = std::make_unique<Widget>();
        
        gui.addWidget(labelId, std::move(label));
        gui.addWidget(imageId, std::move(image));
        gui.addWidget(panelId, std::move(panel));
        gui.addWidget(widgetId, std::move(widget));
        
        // Verify all widgets can be retrieved
        Widget& retrievedLabel = gui.getWidget(labelId);
        Widget& retrievedImage = gui.getWidget(imageId);
        Widget& retrievedPanel = gui.getWidget(panelId);
        Widget& retrievedWidget = gui.getWidget(widgetId);
        
        REQUIRE(retrievedLabel.getType() == "label");
        REQUIRE(retrievedImage.getType() == "image");
        REQUIRE(retrievedPanel.getType() == "panel");
        REQUIRE(retrievedWidget.getType() == "widget");
    }
    
    SECTION("Remove widget") {
        GUI gui;
        
        StringID widgetId("removable");
        auto widget = std::make_unique<Widget>();
        
        gui.addWidget(widgetId, std::move(widget));
        
        // Verify widget exists
        REQUIRE_NOTHROW(gui.getWidget(widgetId));
        
        // Remove widget
        gui.removeWidget(widgetId);
        
        // Verify widget no longer exists
        REQUIRE_THROWS_AS(gui.getWidget(widgetId), std::runtime_error);
    }
    
    SECTION("Get non-existent widget throws exception") {
        GUI gui;
        
        StringID nonExistentId("does_not_exist");
        
        REQUIRE_THROWS_AS(gui.getWidget(nonExistentId), std::runtime_error);
        REQUIRE_THROWS_WITH(gui.getWidget(nonExistentId), Catch::Matchers::ContainsSubstring("Widget not found"));
    }
    
    SECTION("Widget ID collision handling with emplace") {
        GUI gui;
        
        StringID duplicateId("duplicate");
        
        auto widget1 = std::make_unique<Widget>(glm::vec2(10.0f, 10.0f), glm::vec2(50.0f, 50.0f));
        auto widget2 = std::make_unique<Widget>(glm::vec2(20.0f, 20.0f), glm::vec2(60.0f, 60.0f));
        
        gui.addWidget(duplicateId, std::move(widget1));
        
        // Verify first widget
        Widget& firstWidget = gui.getWidget(duplicateId);
        REQUIRE(firstWidget.getPosition() == glm::vec2(10.0f, 10.0f));
        
        // Add second widget with same ID (emplace will insert if key doesn't exist)
        gui.addWidget(duplicateId, std::move(widget2));
        
        // Should still have the first widget (emplace doesn't replace)
        Widget& stillFirstWidget = gui.getWidget(duplicateId);
        REQUIRE(stillFirstWidget.getPosition() == glm::vec2(10.0f, 10.0f));
    }
}

TEST_CASE("GUI update functionality", "[gui][gui_class][update]") {
    SECTION("Update propagates to all widgets") {
        GUI gui;
        
        // Use shared mock setup (CLEANUP)
        MockKeyboard mockKeyboard;
        MockMouse mockMouse;
        GameContext context = createTestGameContext(mockKeyboard, mockMouse);

        // Add widgets at different positions
        StringID widget1Id("widget1");
        StringID widget2Id("widget2");

        auto widget1 = std::make_unique<Widget>(glm::vec2(10.0f, 10.0f), glm::vec2(50.0f, 50.0f));
        auto widget2 = std::make_unique<Widget>(glm::vec2(70.0f, 70.0f), glm::vec2(50.0f, 50.0f));

        gui.addWidget(widget1Id, std::move(widget1));
        gui.addWidget(widget2Id, std::move(widget2));

        // Set up mouse to hover over first widget using shared utility (CLEANUP)
        setupMouseHoverWidget(mockMouse, glm::vec2(10.0f, 10.0f), glm::vec2(50.0f, 50.0f));
        
        gui.update(0.016f, context);
        
        // First widget should be hovered
        REQUIRE(gui.getWidget(widget1Id).isHovered() == true);
        REQUIRE(gui.getWidget(widget2Id).isHovered() == false);
        
        // Move mouse to second widget using shared utility (CLEANUP)
        setupMouseHoverWidget(mockMouse, glm::vec2(70.0f, 70.0f), glm::vec2(50.0f, 50.0f));
        
        gui.update(0.016f, context);
        
        // Second widget should be hovered
        REQUIRE(gui.getWidget(widget1Id).isHovered() == false);
        REQUIRE(gui.getWidget(widget2Id).isHovered() == true);
    }
    
    SECTION("Update with invisible widgets") {
        GUI gui;
        
        MockKeyboard mockKeyboard;
        MockMouse mockMouse;
        GameContext context = createTestGameContext(mockKeyboard, mockMouse);
        
        // Add widget and make it invisible
        StringID widgetId("invisible");
        auto widget = std::make_unique<Widget>(glm::vec2(10.0f, 10.0f), glm::vec2(50.0f, 50.0f));
        widget->setVisible(false);
        
        gui.addWidget(widgetId, std::move(widget));
        
        // Set up mouse to hover over widget
        mockMouse.setPosition(35.0f, 35.0f);
        mockMouse.setButtonPressed(MOUSE_BUTTON_LEFT, true);
        
        gui.update(0.016f, context);
        
        // Widget should not be hovered or pressed because it's invisible
        REQUIRE(gui.getWidget(widgetId).isHovered() == false);
        REQUIRE(gui.getWidget(widgetId).isPressed() == false);
    }
    
    SECTION("Update with complex widget hierarchy") {
        GUI gui;
        
        MockKeyboard mockKeyboard;
        MockMouse mockMouse;
        GameContext context = createTestGameContext(mockKeyboard, mockMouse);
        
        // Add a panel with child widgets
        StringID panelId("main_panel");
        auto panel = std::make_unique<PanelWidget>(glm::vec2(0.0f, 0.0f), glm::vec2(200.0f, 200.0f));
        
        // Add child widgets to the panel
        StringID childId("child_widget");
        auto childWidget = std::make_unique<Widget>(glm::vec2(50.0f, 50.0f), glm::vec2(50.0f, 50.0f));
        panel->addWidget(childId, std::move(childWidget));
        
        gui.addWidget(panelId, std::move(panel));
        
        // Set up mouse to hover over child widget
        mockMouse.setPosition(75.0f, 75.0f); // Center of child widget
        
        gui.update(0.016f, context);
        
        // Both panel and child should be hovered
        REQUIRE(gui.getWidget(panelId).isHovered() == true);
        
        // Access child through panel
        PanelWidget& panelWidget = static_cast<PanelWidget&>(gui.getWidget(panelId));
        REQUIRE(panelWidget.getWidget(childId).isHovered() == true);
    }
}

TEST_CASE("GUI traversal functionality", "[gui][gui_class][traversal]") {
    SECTION("Traverse all widgets") {
        GUI gui;
        
        // Add multiple widgets
        StringID label1Id("label1");
        StringID label2Id("label2");
        StringID imageId("image");
        StringID panelId("panel");
        
        auto label1 = std::make_unique<LabelWidget>(StringID("font::test"), "Label 1");
        auto label2 = std::make_unique<LabelWidget>(StringID("font::test"), "Label 2");
        auto image = std::make_unique<ImageWidget>(StringID("texture::test"));
        auto panel = std::make_unique<PanelWidget>();
        
        gui.addWidget(label1Id, std::move(label1));
        gui.addWidget(label2Id, std::move(label2));
        gui.addWidget(imageId, std::move(image));
        gui.addWidget(panelId, std::move(panel));
        
        // Count widgets and collect types
        int widgetCount = 0;
        std::vector<std::string> types;
        
        gui.traverseWidgets([&widgetCount, &types](Widget& widget) {
            widgetCount++;
            types.push_back(widget.getType());
        });
        
        REQUIRE(widgetCount == 4);
        REQUIRE(types.size() == 4);
        
        // Check that we have the expected types (order may vary due to unordered_map)
        std::sort(types.begin(), types.end());
        std::vector<std::string> expectedTypes = {"image", "label", "label", "panel"};
        std::sort(expectedTypes.begin(), expectedTypes.end());
        REQUIRE(types == expectedTypes);
    }
    
    SECTION("Traverse empty GUI") {
        GUI gui;
        
        int widgetCount = 0;
        gui.traverseWidgets([&widgetCount](Widget& widget) {
            widgetCount++;
        });
        
        REQUIRE(widgetCount == 0);
    }
    
    SECTION("Modify widgets during traversal") {
        GUI gui;
        
        // Add widgets
        StringID widget1Id("widget1");
        StringID widget2Id("widget2");
        
        auto widget1 = std::make_unique<Widget>();
        auto widget2 = std::make_unique<Widget>();
        
        widget1->setVisible(true);
        widget2->setVisible(true);
        
        gui.addWidget(widget1Id, std::move(widget1));
        gui.addWidget(widget2Id, std::move(widget2));
        
        // Hide all widgets during traversal
        gui.traverseWidgets([](Widget& widget) {
            widget.setVisible(false);
        });
        
        // Verify all widgets are now hidden
        REQUIRE_FALSE(gui.getWidget(widget1Id).isVisible());
        REQUIRE_FALSE(gui.getWidget(widget2Id).isVisible());
    }
}

TEST_CASE("GUI edge cases", "[gui][gui_class][edge_cases]") {
    SECTION("Empty StringID for widget") {
        GUI gui;

        StringID emptyId("");
        auto widget = std::make_unique<Widget>();

        gui.addWidget(emptyId, std::move(widget));

        // Should be able to retrieve with empty ID
        REQUIRE_NOTHROW(gui.getWidget(emptyId));

        Widget& retrievedWidget = gui.getWidget(emptyId);
        REQUIRE(retrievedWidget.getType() == "widget");
    }

    SECTION("Very long StringID") {
        GUI gui;

        std::string longIdString(1000, 'a');
        StringID longId(longIdString);
        auto widget = std::make_unique<Widget>();

        gui.addWidget(longId, std::move(widget));

        REQUIRE_NOTHROW(gui.getWidget(longId));
    }

    SECTION("Special characters in StringID") {
        GUI gui;

        std::vector<std::string> specialIds = {
            "widget@#$%",
            "widget with spaces",
            "widget-with-dashes",
            "widget_with_underscores",
            "widget.with.dots",
            "тест", // Unicode
            "🎮"   // Emoji
        };

        for (const auto& idString : specialIds) {
            StringID id(idString);
            auto widget = std::make_unique<Widget>();

            gui.addWidget(id, std::move(widget));
            REQUIRE_NOTHROW(gui.getWidget(id));
        }
    }

    SECTION("Remove non-existent widget") {
        GUI gui;

        StringID nonExistentId("does_not_exist");

        // Should not throw when removing non-existent widget
        REQUIRE_NOTHROW(gui.removeWidget(nonExistentId));
    }

    SECTION("Multiple add and remove operations") {
        GUI gui;

        StringID widgetId("test_widget");

        // Add widget
        auto widget1 = std::make_unique<Widget>(glm::vec2(10.0f, 10.0f), glm::vec2(50.0f, 50.0f));
        gui.addWidget(widgetId, std::move(widget1));
        REQUIRE_NOTHROW(gui.getWidget(widgetId));

        // Remove widget
        gui.removeWidget(widgetId);
        REQUIRE_THROWS_AS(gui.getWidget(widgetId), std::runtime_error);

        // Add widget with same ID again
        auto widget2 = std::make_unique<Widget>(glm::vec2(20.0f, 20.0f), glm::vec2(60.0f, 60.0f));
        gui.addWidget(widgetId, std::move(widget2));

        Widget& newWidget = gui.getWidget(widgetId);
        REQUIRE(newWidget.getPosition() == glm::vec2(20.0f, 20.0f));
        REQUIRE(newWidget.getSize() == glm::vec2(60.0f, 60.0f));
    }
}

TEST_CASE("GUI real-world scenarios", "[gui][gui_class][real_world]") {
    SECTION("Complete game menu system") {
        GUI gui;

        // Main menu
        StringID mainMenuId("main_menu");
        auto mainMenu = std::make_unique<PanelWidget>(glm::vec2(100.0f, 100.0f), glm::vec2(400.0f, 300.0f));

        // Add menu buttons to the panel
        StringID startButtonId("start_button");
        StringID optionsButtonId("options_button");
        StringID exitButtonId("exit_button");

        auto startButton = std::make_unique<LabelWidget>(StringID("font::menu"), "Start Game");
        auto optionsButton = std::make_unique<LabelWidget>(StringID("font::menu"), "Options");
        auto exitButton = std::make_unique<LabelWidget>(StringID("font::menu"), "Exit");

        startButton->setPosition(glm::vec2(150.0f, 150.0f));
        startButton->setSize(glm::vec2(120.0f, 40.0f));
        optionsButton->setPosition(glm::vec2(150.0f, 200.0f));
        optionsButton->setSize(glm::vec2(120.0f, 40.0f));
        exitButton->setPosition(glm::vec2(150.0f, 250.0f));
        exitButton->setSize(glm::vec2(120.0f, 40.0f));

        mainMenu->addWidget(startButtonId, std::move(startButton));
        mainMenu->addWidget(optionsButtonId, std::move(optionsButton));
        mainMenu->addWidget(exitButtonId, std::move(exitButton));

        gui.addWidget(mainMenuId, std::move(mainMenu));

        // HUD overlay
        StringID hudId("hud");
        auto hud = std::make_unique<PanelWidget>(glm::vec2(0.0f, 0.0f), glm::vec2(800.0f, 100.0f));

        StringID healthBarId("health_bar");
        StringID scoreId("score");

        auto healthBar = std::make_unique<ImageWidget>(StringID("texture::ui::health_bar"));
        auto scoreLabel = std::make_unique<LabelWidget>(StringID("font::hud"), "Score: 0");

        healthBar->setPosition(glm::vec2(10.0f, 10.0f));
        scoreLabel->setPosition(glm::vec2(250.0f, 10.0f));

        hud->addWidget(healthBarId, std::move(healthBar));
        hud->addWidget(scoreId, std::move(scoreLabel));

        gui.addWidget(hudId, std::move(hud));

        // Verify complete system
        REQUIRE(gui.getWidget(mainMenuId).getType() == "panel");
        REQUIRE(gui.getWidget(hudId).getType() == "panel");

        // Access nested widgets
        PanelWidget& mainMenuPanel = static_cast<PanelWidget&>(gui.getWidget(mainMenuId));
        PanelWidget& hudPanel = static_cast<PanelWidget&>(gui.getWidget(hudId));

        REQUIRE(mainMenuPanel.getWidget(startButtonId).getType() == "label");
        REQUIRE(hudPanel.getWidget(healthBarId).getType() == "image");

        // Test interaction
        MockKeyboard mockKeyboard;
        MockMouse mockMouse;
        GameContext context = createTestGameContext(mockKeyboard, mockMouse);

        // Click on start button - note: child widget positions are in global coordinates
        // Start button is at (150, 150) in global coordinates
        mockMouse.setPosition(210.0f, 170.0f); // Center of start button
        mockMouse.setButtonPressed(MOUSE_BUTTON_LEFT, true);

        gui.update(0.016f, context);

        // The click should work because the child widget position is already in global coordinates
        REQUIRE(mainMenuPanel.getWidget(startButtonId).isPressed());
    }

    SECTION("Dynamic GUI state management") {
        GUI gui;

        // Game state: Menu
        StringID menuPanelId("menu_panel");
        auto menuPanel = std::make_unique<PanelWidget>(glm::vec2(200.0f, 150.0f), glm::vec2(400.0f, 300.0f));

        StringID playButtonId("play_button");
        auto playButton = std::make_unique<LabelWidget>(StringID("font::menu"), "Play");
        menuPanel->addWidget(playButtonId, std::move(playButton));

        gui.addWidget(menuPanelId, std::move(menuPanel));

        // Verify menu is active
        REQUIRE(gui.getWidget(menuPanelId).isVisible());

        // Transition to game state: remove menu, add HUD
        gui.removeWidget(menuPanelId);
        REQUIRE_THROWS_AS(gui.getWidget(menuPanelId), std::runtime_error);

        StringID gameHudId("game_hud");
        auto gameHud = std::make_unique<PanelWidget>(glm::vec2(0.0f, 0.0f), glm::vec2(800.0f, 600.0f));

        StringID healthId("health");
        StringID ammoId("ammo");

        auto healthDisplay = std::make_unique<LabelWidget>(StringID("font::hud"), "Health: 100");
        auto ammoDisplay = std::make_unique<LabelWidget>(StringID("font::hud"), "Ammo: 30/90");

        gameHud->addWidget(healthId, std::move(healthDisplay));
        gameHud->addWidget(ammoId, std::move(ammoDisplay));

        gui.addWidget(gameHudId, std::move(gameHud));

        // Verify game HUD is active
        REQUIRE(gui.getWidget(gameHudId).isVisible());

        PanelWidget& hudPanel = static_cast<PanelWidget&>(gui.getWidget(gameHudId));
        LabelWidget& healthLabel = static_cast<LabelWidget&>(hudPanel.getWidget(healthId));

        REQUIRE(healthLabel.getText() == "Health: 100");

        // Update health
        healthLabel.setText("Health: 75");
        REQUIRE(healthLabel.getText() == "Health: 75");
    }

    SECTION("GUI performance with many widgets") {
        GUI gui;

        const int numWidgets = 100;

        // Add many widgets
        for (int i = 0; i < numWidgets; ++i) {
            StringID widgetId("widget_" + std::to_string(i));
            auto widget = std::make_unique<Widget>(
                glm::vec2(i * 10.0f, i * 5.0f),
                glm::vec2(50.0f, 30.0f)
            );
            gui.addWidget(widgetId, std::move(widget));
        }

        // Verify all widgets can be accessed
        for (int i = 0; i < numWidgets; ++i) {
            StringID widgetId("widget_" + std::to_string(i));
            REQUIRE_NOTHROW(gui.getWidget(widgetId));

            Widget& widget = gui.getWidget(widgetId);
            REQUIRE(widget.getPosition().x == i * 10.0f);
            REQUIRE(widget.getPosition().y == i * 5.0f);
        }

        // Test traversal performance
        int traversalCount = 0;
        gui.traverseWidgets([&traversalCount](Widget& widget) {
            traversalCount++;
            widget.setVisible(traversalCount % 2 == 0); // Hide every other widget
        });

        REQUIRE(traversalCount == numWidgets);

        // Test update performance
        MockKeyboard mockKeyboard;
        MockMouse mockMouse;
        GameContext context = createTestGameContext(mockKeyboard, mockMouse);

        // This should complete without performance issues
        gui.update(0.016f, context);

        // Remove half the widgets
        for (int i = 0; i < numWidgets / 2; ++i) {
            StringID widgetId("widget_" + std::to_string(i));
            gui.removeWidget(widgetId);
        }

        // Verify remaining widgets
        for (int i = numWidgets / 2; i < numWidgets; ++i) {
            StringID widgetId("widget_" + std::to_string(i));
            REQUIRE_NOTHROW(gui.getWidget(widgetId));
        }
    }

    SECTION("GUI layering and z-order simulation") {
        GUI gui;

        // Background layer
        StringID backgroundId("background");
        auto background = std::make_unique<ImageWidget>(StringID("texture::background"));
        background->setPosition(glm::vec2(0.0f, 0.0f));
        background->setSize(glm::vec2(800.0f, 600.0f));
        gui.addWidget(backgroundId, std::move(background));

        // Game world layer (panel with game objects)
        StringID gameWorldId("game_world");
        auto gameWorld = std::make_unique<PanelWidget>(glm::vec2(0.0f, 0.0f), glm::vec2(800.0f, 600.0f));

        StringID playerId("player");
        StringID enemyId("enemy");

        auto playerSprite = std::make_unique<ImageWidget>(StringID("texture::player"));
        auto enemySprite = std::make_unique<ImageWidget>(StringID("texture::enemy"));

        playerSprite->setPosition(glm::vec2(100.0f, 300.0f));
        enemySprite->setPosition(glm::vec2(500.0f, 200.0f));

        gameWorld->addWidget(playerId, std::move(playerSprite));
        gameWorld->addWidget(enemyId, std::move(enemySprite));

        gui.addWidget(gameWorldId, std::move(gameWorld));

        // UI overlay layer
        StringID uiOverlayId("ui_overlay");
        auto uiOverlay = std::make_unique<PanelWidget>(glm::vec2(0.0f, 0.0f), glm::vec2(800.0f, 600.0f));

        StringID pauseButtonId("pause_button");
        auto pauseButton = std::make_unique<LabelWidget>(StringID("font::ui"), "Pause");
        pauseButton->setPosition(glm::vec2(750.0f, 10.0f));
        pauseButton->setSize(glm::vec2(40.0f, 30.0f));

        uiOverlay->addWidget(pauseButtonId, std::move(pauseButton));
        gui.addWidget(uiOverlayId, std::move(uiOverlay));

        // Verify layered structure
        REQUIRE(gui.getWidget(backgroundId).getType() == "image");
        REQUIRE(gui.getWidget(gameWorldId).getType() == "panel");
        REQUIRE(gui.getWidget(uiOverlayId).getType() == "panel");

        // Test interaction with top layer
        MockKeyboard mockKeyboard;
        MockMouse mockMouse;
        GameContext context = createTestGameContext(mockKeyboard, mockMouse);

        // Click on pause button (top layer) - button is at (750, 10) with size (40, 30)
        mockMouse.setPosition(770.0f, 25.0f); // Center of pause button
        mockMouse.setButtonPressed(MOUSE_BUTTON_LEFT, true);

        gui.update(0.016f, context);

        PanelWidget& overlay = static_cast<PanelWidget&>(gui.getWidget(uiOverlayId));
        REQUIRE(overlay.getWidget(pauseButtonId).isPressed());
    }
}

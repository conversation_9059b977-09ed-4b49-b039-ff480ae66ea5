#include <catch2/catch_test_macros.hpp>
#include <catch2/catch_approx.hpp>

#include <glm/gtc/matrix_transform.hpp>

#include "utils/collision_math.hpp"
#include "../test_utils.hpp"

using namespace IronFrost;
using namespace IronFrost::CollisionMath;
using Catch::Approx;

TEST_CASE("Plane construction and basic operations", "[utils][collision_math][plane]") {
    SECTION("Plane from normal and distance") {
        glm::vec3 normal(0.0f, 1.0f, 0.0f);
        float distance = 5.0f;
        Plane plane(normal, distance);
        
        REQUIRE(isVec3Equal(plane.normal, normal));
        REQUIRE(plane.distance == Approx(distance));
    }
    
    SECTION("Plane from point and normal") {
        glm::vec3 point(0.0f, 5.0f, 0.0f);
        glm::vec3 normal(0.0f, 1.0f, 0.0f);
        Plane plane(point, normal);
        
        REQUIRE(isVec3Equal(plane.normal, normal));
        REQUIRE(plane.distance == Approx(5.0f));
    }
    
    SECTION("Plane from three points") {
        glm::vec3 p1(0.0f, 0.0f, 0.0f);
        glm::vec3 p2(1.0f, 0.0f, 0.0f);
        glm::vec3 p3(0.0f, 0.0f, 1.0f);
        Plane plane(p1, p2, p3);

        // Should create a plane with normal pointing down (Y-) due to winding order
        REQUIRE(isVec3Equal(plane.normal, glm::vec3(0.0f, -1.0f, 0.0f)));
        REQUIRE(plane.distance == Approx(0.0f));
    }
    
    SECTION("Normal vector is normalized") {
        glm::vec3 unnormalizedNormal(0.0f, 5.0f, 0.0f);
        Plane plane(unnormalizedNormal, 1.0f);
        
        REQUIRE(isVec3Normalized(plane.normal));
        REQUIRE(isVec3Equal(plane.normal, glm::vec3(0.0f, 1.0f, 0.0f)));
    }
}

TEST_CASE("Distance to plane calculations", "[utils][collision_math][plane][distance]") {
    Plane horizontalPlane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // XZ plane at Y=0
    
    SECTION("Point above plane") {
        glm::vec3 point(0.0f, 5.0f, 0.0f);
        float distance = distanceToPlane(point, horizontalPlane);
        
        REQUIRE(distance == Approx(5.0f));
        REQUIRE(isPointAbovePlane(point, horizontalPlane));
        REQUIRE_FALSE(isPointBelowPlane(point, horizontalPlane));
    }
    
    SECTION("Point below plane") {
        glm::vec3 point(0.0f, -3.0f, 0.0f);
        float distance = distanceToPlane(point, horizontalPlane);
        
        REQUIRE(distance == Approx(-3.0f));
        REQUIRE_FALSE(isPointAbovePlane(point, horizontalPlane));
        REQUIRE(isPointBelowPlane(point, horizontalPlane));
    }
    
    SECTION("Point on plane") {
        glm::vec3 point(10.0f, 0.0f, -5.0f);
        float distance = distanceToPlane(point, horizontalPlane);
        
        REQUIRE(distance == Approx(0.0f));
        REQUIRE_FALSE(isPointAbovePlane(point, horizontalPlane));
        REQUIRE_FALSE(isPointBelowPlane(point, horizontalPlane));
    }
}

TEST_CASE("Point projection onto plane", "[utils][collision_math][plane][projection]") {
    Plane horizontalPlane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // XZ plane at Y=0
    
    SECTION("Project point above plane") {
        glm::vec3 point(5.0f, 10.0f, -3.0f);
        glm::vec3 projected = projectPointOntoPlane(point, horizontalPlane);
        
        REQUIRE(isVec3Equal(projected, glm::vec3(5.0f, 0.0f, -3.0f)));
    }
    
    SECTION("Project point below plane") {
        glm::vec3 point(2.0f, -7.0f, 8.0f);
        glm::vec3 projected = projectPointOntoPlane(point, horizontalPlane);
        
        REQUIRE(isVec3Equal(projected, glm::vec3(2.0f, 0.0f, 8.0f)));
    }
    
    SECTION("Project point on plane") {
        glm::vec3 point(1.0f, 0.0f, 1.0f);
        glm::vec3 projected = projectPointOntoPlane(point, horizontalPlane);
        
        REQUIRE(isVec3Equal(projected, point));
    }
}

TEST_CASE("Ray-plane intersection", "[utils][collision_math][ray][plane][intersection]") {
    Plane horizontalPlane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // XZ plane at Y=0
    
    SECTION("Ray intersects plane from above") {
        Ray ray(glm::vec3(0.0f, 5.0f, 0.0f), glm::vec3(0.0f, -1.0f, 0.0f));
        auto result = rayPlaneIntersection(ray, horizontalPlane);
        
        REQUIRE(result.has_value());
        REQUIRE(result.value() == Approx(5.0f));
        
        // Verify intersection point
        glm::vec3 intersectionPoint = ray.origin + result.value() * ray.direction;
        REQUIRE(isVec3Equal(intersectionPoint, glm::vec3(0.0f, 0.0f, 0.0f)));
    }
    
    SECTION("Ray intersects plane from below") {
        Ray ray(glm::vec3(2.0f, -3.0f, 1.0f), glm::vec3(0.0f, 1.0f, 0.0f));
        auto result = rayPlaneIntersection(ray, horizontalPlane);
        
        REQUIRE(result.has_value());
        REQUIRE(result.value() == Approx(3.0f));
        
        // Verify intersection point
        glm::vec3 intersectionPoint = ray.origin + result.value() * ray.direction;
        REQUIRE(isVec3Equal(intersectionPoint, glm::vec3(2.0f, 0.0f, 1.0f)));
    }
    
    SECTION("Ray parallel to plane") {
        Ray ray(glm::vec3(0.0f, 5.0f, 0.0f), glm::vec3(1.0f, 0.0f, 0.0f));
        auto result = rayPlaneIntersection(ray, horizontalPlane);
        
        REQUIRE_FALSE(result.has_value());
    }
    
    SECTION("Ray pointing away from plane") {
        Ray ray(glm::vec3(0.0f, 5.0f, 0.0f), glm::vec3(0.0f, 1.0f, 0.0f));
        auto result = rayPlaneIntersection(ray, horizontalPlane);
        
        REQUIRE_FALSE(result.has_value());
    }
}

TEST_CASE("AABB construction and basic operations", "[utils][collision_math][aabb]") {
    SECTION("AABB from min and max") {
        glm::vec3 min(-1.0f, -2.0f, -3.0f);
        glm::vec3 max(1.0f, 2.0f, 3.0f);
        AABB aabb(min, max);
        
        REQUIRE(isVec3Equal(aabb.min, min));
        REQUIRE(isVec3Equal(aabb.max, max));
    }
    
    SECTION("AABB from center and size") {
        glm::vec3 center(5.0f, 10.0f, -2.0f);
        glm::vec3 size(4.0f, 6.0f, 8.0f);
        AABB aabb = AABB::fromCenterAndSize(center, size);
        
        REQUIRE(isVec3Equal(aabb.getCenter(), center));
        REQUIRE(isVec3Equal(aabb.getSize(), size));
        REQUIRE(isVec3Equal(aabb.min, glm::vec3(3.0f, 7.0f, -6.0f)));
        REQUIRE(isVec3Equal(aabb.max, glm::vec3(7.0f, 13.0f, 2.0f)));
    }
}

TEST_CASE("Point in AABB tests", "[utils][collision_math][aabb][point]") {
    AABB aabb(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));
    
    SECTION("Point inside AABB") {
        REQUIRE(isPointInAABB(glm::vec3(0.0f, 0.0f, 0.0f), aabb));
        REQUIRE(isPointInAABB(glm::vec3(0.5f, -0.5f, 0.8f), aabb));
    }
    
    SECTION("Point on AABB boundary") {
        REQUIRE(isPointInAABB(glm::vec3(1.0f, 0.0f, 0.0f), aabb));
        REQUIRE(isPointInAABB(glm::vec3(-1.0f, 1.0f, -1.0f), aabb));
    }
    
    SECTION("Point outside AABB") {
        REQUIRE_FALSE(isPointInAABB(glm::vec3(2.0f, 0.0f, 0.0f), aabb));
        REQUIRE_FALSE(isPointInAABB(glm::vec3(0.0f, -2.0f, 0.0f), aabb));
        REQUIRE_FALSE(isPointInAABB(glm::vec3(0.0f, 0.0f, 2.0f), aabb));
    }
}

TEST_CASE("AABB intersection tests", "[utils][collision_math][aabb][intersection]") {
    AABB aabb1(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));

    SECTION("Overlapping AABBs") {
        AABB aabb2(glm::vec3(0.0f, 0.0f, 0.0f), glm::vec3(2.0f, 2.0f, 2.0f));
        REQUIRE(aabbIntersection(aabb1, aabb2));
        REQUIRE(aabbIntersection(aabb2, aabb1)); // Should be symmetric
    }

    SECTION("Non-overlapping AABBs") {
        AABB aabb2(glm::vec3(2.0f, 2.0f, 2.0f), glm::vec3(3.0f, 3.0f, 3.0f));
        REQUIRE_FALSE(aabbIntersection(aabb1, aabb2));
        REQUIRE_FALSE(aabbIntersection(aabb2, aabb1)); // Should be symmetric
    }

    SECTION("Touching AABBs") {
        AABB aabb2(glm::vec3(1.0f, -1.0f, -1.0f), glm::vec3(2.0f, 1.0f, 1.0f));
        REQUIRE(aabbIntersection(aabb1, aabb2)); // Touching counts as intersection
    }
}

TEST_CASE("Distance calculations", "[utils][collision_math][distance]") {
    AABB aabb(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));

    SECTION("Distance to AABB from outside") {
        glm::vec3 point(3.0f, 0.0f, 0.0f);
        float distance = distanceToAABB(point, aabb);
        REQUIRE(distance == Approx(2.0f)); // Distance from (3,0,0) to (1,0,0)
    }

    SECTION("Distance to AABB from inside") {
        glm::vec3 point(0.0f, 0.0f, 0.0f); // Center of AABB
        float distance = distanceToAABB(point, aabb);
        REQUIRE(distance == Approx(0.0f)); // Point is inside
    }

    SECTION("Closest point on AABB") {
        glm::vec3 point(3.0f, 2.0f, -2.0f);
        glm::vec3 closest = closestPointOnAABB(point, aabb);
        REQUIRE(isVec3Equal(closest, glm::vec3(1.0f, 1.0f, -1.0f)));
    }
}

TEST_CASE("Sphere construction and basic operations", "[utils][collision_math][sphere]") {
    SECTION("Sphere construction") {
        glm::vec3 center(1.0f, 2.0f, 3.0f);
        float radius = 5.0f;
        Sphere sphere(center, radius);

        REQUIRE(isVec3Equal(sphere.center, center));
        REQUIRE(sphere.radius == Approx(radius));
    }

    SECTION("Default sphere construction") {
        Sphere sphere;

        REQUIRE(isVec3Equal(sphere.center, glm::vec3(0.0f)));
        REQUIRE(sphere.radius == Approx(0.0f));
    }
}

TEST_CASE("Sphere intersection tests", "[utils][collision_math][sphere][intersection]") {
    SECTION("Overlapping spheres") {
        Sphere sphere1(glm::vec3(0.0f, 0.0f, 0.0f), 2.0f);
        Sphere sphere2(glm::vec3(3.0f, 0.0f, 0.0f), 2.0f);

        REQUIRE(sphereIntersection(sphere1, sphere2));
        REQUIRE(sphereIntersection(sphere2, sphere1)); // Should be symmetric
    }

    SECTION("Non-overlapping spheres") {
        Sphere sphere1(glm::vec3(0.0f, 0.0f, 0.0f), 1.0f);
        Sphere sphere2(glm::vec3(5.0f, 0.0f, 0.0f), 1.0f);

        REQUIRE_FALSE(sphereIntersection(sphere1, sphere2));
        REQUIRE_FALSE(sphereIntersection(sphere2, sphere1)); // Should be symmetric
    }

    SECTION("Touching spheres") {
        Sphere sphere1(glm::vec3(0.0f, 0.0f, 0.0f), 2.0f);
        Sphere sphere2(glm::vec3(4.0f, 0.0f, 0.0f), 2.0f);

        REQUIRE(sphereIntersection(sphere1, sphere2)); // Touching counts as intersection
    }

    SECTION("Identical spheres") {
        Sphere sphere1(glm::vec3(1.0f, 1.0f, 1.0f), 3.0f);
        Sphere sphere2(glm::vec3(1.0f, 1.0f, 1.0f), 3.0f);

        REQUIRE(sphereIntersection(sphere1, sphere2));
    }
}

TEST_CASE("Sphere-plane intersection tests", "[utils][collision_math][sphere][plane][intersection]") {
    Plane horizontalPlane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // XZ plane at Y=0

    SECTION("Sphere intersects plane") {
        Sphere sphere(glm::vec3(0.0f, 1.0f, 0.0f), 2.0f); // Center above plane, radius crosses plane
        REQUIRE(spherePlaneIntersection(sphere, horizontalPlane));
    }

    SECTION("Sphere above plane, no intersection") {
        Sphere sphere(glm::vec3(0.0f, 5.0f, 0.0f), 2.0f); // Center well above plane
        REQUIRE_FALSE(spherePlaneIntersection(sphere, horizontalPlane));
    }

    SECTION("Sphere below plane") {
        Sphere sphere(glm::vec3(0.0f, -3.0f, 0.0f), 2.0f); // Center below plane
        REQUIRE(spherePlaneIntersection(sphere, horizontalPlane)); // Should intersect since it's below
    }

    SECTION("Sphere touching plane") {
        Sphere sphere(glm::vec3(0.0f, 2.0f, 0.0f), 2.0f); // Sphere just touching plane
        REQUIRE(spherePlaneIntersection(sphere, horizontalPlane));
    }
}

TEST_CASE("AABB-plane intersection tests", "[utils][collision_math][aabb][plane][intersection]") {
    Plane horizontalPlane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // XZ plane at Y=0

    SECTION("AABB intersects plane") {
        AABB aabb(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f)); // Crosses plane
        REQUIRE(aabbPlaneIntersection(aabb, horizontalPlane));
    }

    SECTION("AABB above plane, no intersection") {
        AABB aabb(glm::vec3(-1.0f, 2.0f, -1.0f), glm::vec3(1.0f, 4.0f, 1.0f)); // Entirely above plane
        REQUIRE_FALSE(aabbPlaneIntersection(aabb, horizontalPlane));
    }

    SECTION("AABB below plane") {
        AABB aabb(glm::vec3(-1.0f, -4.0f, -1.0f), glm::vec3(1.0f, -2.0f, 1.0f)); // Entirely below plane
        REQUIRE_FALSE(aabbPlaneIntersection(aabb, horizontalPlane)); // Should not intersect since it doesn't straddle the plane
    }

    SECTION("AABB touching plane") {
        AABB aabb(glm::vec3(-1.0f, 0.0f, -1.0f), glm::vec3(1.0f, 2.0f, 1.0f)); // Bottom edge on plane
        REQUIRE(aabbPlaneIntersection(aabb, horizontalPlane));
    }
}

TEST_CASE("Sphere-AABB intersection tests", "[utils][collision_math][sphere][aabb][intersection]") {
    AABB aabb(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));

    SECTION("Sphere intersects AABB") {
        Sphere sphere(glm::vec3(0.0f, 0.0f, 0.0f), 1.5f); // Center inside AABB
        REQUIRE(sphereAABBIntersection(sphere, aabb));
    }

    SECTION("Sphere outside AABB, no intersection") {
        Sphere sphere(glm::vec3(5.0f, 0.0f, 0.0f), 1.0f); // Far from AABB
        REQUIRE_FALSE(sphereAABBIntersection(sphere, aabb));
    }

    SECTION("Sphere touching AABB corner") {
        Sphere sphere(glm::vec3(2.0f, 2.0f, 2.0f), glm::sqrt(3.0f)); // Distance to corner (1,1,1) is sqrt(3)
        REQUIRE(sphereAABBIntersection(sphere, aabb));
    }

    SECTION("Sphere touching AABB face") {
        Sphere sphere(glm::vec3(2.5f, 0.0f, 0.0f), 1.5f); // Touching right face
        REQUIRE(sphereAABBIntersection(sphere, aabb));
    }
}

TEST_CASE("Penetration calculations", "[utils][collision_math][penetration]") {
    SECTION("Sphere-plane penetration") {
        Plane horizontalPlane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // XZ plane at Y=0

        SECTION("Sphere penetrating plane") {
            Sphere sphere(glm::vec3(0.0f, 1.0f, 0.0f), 2.0f); // Center 1 unit above, radius 2
            float penetration = spherePlanePenetration(sphere, horizontalPlane);
            REQUIRE(penetration == Approx(1.0f)); // 2 - 1 = 1
        }

        SECTION("Sphere not penetrating plane") {
            Sphere sphere(glm::vec3(0.0f, 5.0f, 0.0f), 2.0f); // Center well above plane
            float penetration = spherePlanePenetration(sphere, horizontalPlane);
            REQUIRE(penetration == Approx(0.0f));
        }

        SECTION("Sphere center below plane") {
            Sphere sphere(glm::vec3(0.0f, -1.0f, 0.0f), 2.0f); // Center 1 unit below, radius 2
            float penetration = spherePlanePenetration(sphere, horizontalPlane);
            REQUIRE(penetration == Approx(3.0f)); // 2 - (-1) = 3
        }
    }

    SECTION("Sphere-sphere penetration") {
        SECTION("Overlapping spheres") {
            Sphere sphere1(glm::vec3(0.0f, 0.0f, 0.0f), 2.0f);
            Sphere sphere2(glm::vec3(3.0f, 0.0f, 0.0f), 2.0f); // Distance = 3, radii sum = 4
            float penetration = sphereSpherePenetration(sphere1, sphere2);
            REQUIRE(penetration == Approx(1.0f)); // 4 - 3 = 1
        }

        SECTION("Non-overlapping spheres") {
            Sphere sphere1(glm::vec3(0.0f, 0.0f, 0.0f), 1.0f);
            Sphere sphere2(glm::vec3(5.0f, 0.0f, 0.0f), 1.0f); // Distance = 5, radii sum = 2
            float penetration = sphereSpherePenetration(sphere1, sphere2);
            REQUIRE(penetration == Approx(0.0f));
        }

        SECTION("Identical spheres") {
            Sphere sphere1(glm::vec3(1.0f, 1.0f, 1.0f), 3.0f);
            Sphere sphere2(glm::vec3(1.0f, 1.0f, 1.0f), 3.0f); // Distance = 0, radii sum = 6
            float penetration = sphereSpherePenetration(sphere1, sphere2);
            REQUIRE(penetration == Approx(6.0f));
        }
    }

    SECTION("Sphere-AABB penetration") {
        AABB aabb(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));

        SECTION("Sphere center inside AABB") {
            Sphere sphere(glm::vec3(0.0f, 0.0f, 0.0f), 2.0f); // Center at AABB center
            float penetration = sphereAABBPenetration(sphere, aabb);
            REQUIRE(penetration == Approx(2.0f)); // Distance to AABB = 0, radius = 2
        }

        SECTION("Sphere overlapping AABB from outside") {
            Sphere sphere(glm::vec3(2.0f, 0.0f, 0.0f), 1.5f); // Distance to AABB = 1, radius = 1.5
            float penetration = sphereAABBPenetration(sphere, aabb);
            REQUIRE(penetration == Approx(0.5f)); // 1.5 - 1 = 0.5
        }

        SECTION("Sphere not overlapping AABB") {
            Sphere sphere(glm::vec3(5.0f, 0.0f, 0.0f), 1.0f); // Distance to AABB = 4, radius = 1
            float penetration = sphereAABBPenetration(sphere, aabb);
            REQUIRE(penetration == Approx(0.0f));
        }
    }
}

TEST_CASE("Collision resolution", "[utils][collision_math][resolution]") {
    SECTION("Sphere-plane collision resolution") {
        Plane horizontalPlane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // XZ plane at Y=0

        SECTION("Sphere penetrating plane from above") {
            Sphere sphere(glm::vec3(0.0f, 1.0f, 0.0f), 2.0f); // Center 1 unit above, radius 2
            glm::vec3 resolved = resolveSpherePlaneCollision(sphere, horizontalPlane);
            REQUIRE(isVec3Equal(resolved, glm::vec3(0.0f, 2.0f, 0.0f))); // Should move to Y=2
        }

        SECTION("Sphere not penetrating plane") {
            Sphere sphere(glm::vec3(0.0f, 5.0f, 0.0f), 2.0f); // No penetration
            glm::vec3 resolved = resolveSpherePlaneCollision(sphere, horizontalPlane);
            REQUIRE(isVec3Equal(resolved, sphere.center)); // Should remain unchanged
        }

        SECTION("Sphere penetrating plane from below") {
            Sphere sphere(glm::vec3(0.0f, -1.0f, 0.0f), 2.0f); // Center below plane
            glm::vec3 resolved = resolveSpherePlaneCollision(sphere, horizontalPlane);
            REQUIRE(isVec3Equal(resolved, glm::vec3(0.0f, 2.0f, 0.0f))); // Should move to Y=2
        }
    }

    SECTION("Sphere-sphere collision resolution") {
        SECTION("Overlapping spheres") {
            Sphere sphereA(glm::vec3(0.0f, 0.0f, 0.0f), 2.0f);
            Sphere sphereB(glm::vec3(3.0f, 0.0f, 0.0f), 2.0f); // Distance = 3, should be 4
            glm::vec3 resolved = resolveSphereSphereCollision(sphereA, sphereB);

            // Should move sphereA away from sphereB along X-axis
            REQUIRE(resolved.x < sphereA.center.x); // Moved in negative X direction
            REQUIRE(isFloatEqual(resolved.y, sphereA.center.y)); // Y unchanged
            REQUIRE(isFloatEqual(resolved.z, sphereA.center.z)); // Z unchanged

            // Check that the distance is now correct (approximately)
            float newDistance = glm::length(resolved - sphereB.center);
            REQUIRE(newDistance >= 4.0f - 0.01f); // Should be at least sum of radii
        }

        SECTION("Non-overlapping spheres") {
            Sphere sphereA(glm::vec3(0.0f, 0.0f, 0.0f), 1.0f);
            Sphere sphereB(glm::vec3(5.0f, 0.0f, 0.0f), 1.0f); // No overlap
            glm::vec3 resolved = resolveSphereSphereCollision(sphereA, sphereB);
            REQUIRE(isVec3Equal(resolved, sphereA.center)); // Should remain unchanged
        }

        SECTION("Identical sphere centers") {
            Sphere sphereA(glm::vec3(1.0f, 1.0f, 1.0f), 2.0f);
            Sphere sphereB(glm::vec3(1.0f, 1.0f, 1.0f), 3.0f); // Same center
            glm::vec3 resolved = resolveSphereSphereCollision(sphereA, sphereB);

            // Should move along X-axis by default
            REQUIRE(resolved.x > sphereA.center.x);
            REQUIRE(isFloatEqual(resolved.y, sphereA.center.y));
            REQUIRE(isFloatEqual(resolved.z, sphereA.center.z));
        }
    }

    SECTION("Sphere-AABB collision resolution") {
        AABB aabb(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));

        SECTION("Sphere overlapping AABB from outside") {
            Sphere sphere(glm::vec3(2.0f, 0.0f, 0.0f), 1.5f); // Overlapping from right side
            glm::vec3 resolved = resolveSphereAABBCollision(sphere, aabb);

            // Should move sphere away from AABB along X-axis
            REQUIRE(resolved.x > sphere.center.x);
            REQUIRE(isFloatEqual(resolved.y, sphere.center.y));
            REQUIRE(isFloatEqual(resolved.z, sphere.center.z));
        }

        SECTION("Sphere center inside AABB") {
            Sphere sphere(glm::vec3(0.0f, 0.0f, 0.0f), 2.0f); // Center inside AABB
            glm::vec3 resolved = resolveSphereAABBCollision(sphere, aabb);

            // Should be pushed out along one of the axes
            REQUIRE_FALSE(isVec3Equal(resolved, sphere.center));
        }

        SECTION("Sphere not overlapping AABB") {
            Sphere sphere(glm::vec3(5.0f, 0.0f, 0.0f), 1.0f); // No overlap
            glm::vec3 resolved = resolveSphereAABBCollision(sphere, aabb);
            REQUIRE(isVec3Equal(resolved, sphere.center)); // Should remain unchanged
        }
    }

    SECTION("AABB-sphere collision resolution") {
        Sphere sphere(glm::vec3(0.0f, 0.0f, 0.0f), 2.0f);

        SECTION("AABB overlapping sphere") {
            AABB aabb(glm::vec3(-0.5f, -0.5f, -0.5f), glm::vec3(0.5f, 0.5f, 0.5f)); // Small AABB inside sphere
            glm::vec3 resolved = resolveAABBSphereCollision(aabb, sphere);

            // AABB center should be moved away from sphere center
            REQUIRE_FALSE(isVec3Equal(resolved, aabb.getCenter()));
        }

        SECTION("AABB not overlapping sphere") {
            AABB aabb(glm::vec3(5.0f, 5.0f, 5.0f), glm::vec3(6.0f, 6.0f, 6.0f)); // Far from sphere
            glm::vec3 resolved = resolveAABBSphereCollision(aabb, sphere);
            REQUIRE(isVec3Equal(resolved, aabb.getCenter())); // Should remain unchanged
        }
    }

    SECTION("AABB-AABB collision resolution") {
        SECTION("Overlapping AABBs") {
            AABB aabbA(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));
            AABB aabbB(glm::vec3(0.0f, 0.0f, 0.0f), glm::vec3(2.0f, 2.0f, 2.0f)); // Overlapping
            glm::vec3 resolved = resolveAABBAABBCollision(aabbA, aabbB);

            // Should move aabbA away from aabbB
            REQUIRE_FALSE(isVec3Equal(resolved, aabbA.getCenter()));
        }

        SECTION("Non-overlapping AABBs") {
            AABB aabbA(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));
            AABB aabbB(glm::vec3(5.0f, 5.0f, 5.0f), glm::vec3(7.0f, 7.0f, 7.0f)); // No overlap
            glm::vec3 resolved = resolveAABBAABBCollision(aabbA, aabbB);
            REQUIRE(isVec3Equal(resolved, aabbA.getCenter())); // Should remain unchanged
        }

        SECTION("Slightly overlapping AABBs") {
            AABB aabbA(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));
            AABB aabbB(glm::vec3(0.5f, -1.0f, -1.0f), glm::vec3(2.5f, 1.0f, 1.0f)); // Overlapping by 0.5 units on X
            glm::vec3 resolved = resolveAABBAABBCollision(aabbA, aabbB);

            // Should move away from aabbB along X-axis
            REQUIRE(resolved.x < aabbA.getCenter().x);
        }
    }
}

TEST_CASE("Transformation functions", "[utils][collision_math][transform]") {
    SECTION("Plane transformation") {
        Plane originalPlane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // XZ plane at Y=0

        SECTION("Translation") {
            glm::mat4 translation = glm::translate(glm::mat4(1.0f), glm::vec3(0.0f, 5.0f, 0.0f));
            Plane transformed = transformPlane(originalPlane, translation);

            REQUIRE(isVec3Equal(transformed.normal, originalPlane.normal)); // Normal unchanged
            REQUIRE(transformed.distance == Approx(5.0f)); // Moved up by 5 units
        }

        SECTION("Rotation") {
            glm::mat4 rotation = glm::rotate(glm::mat4(1.0f), glm::radians(90.0f), glm::vec3(0.0f, 0.0f, 1.0f));
            Plane transformed = transformPlane(originalPlane, rotation);

            // Y-up normal should become X-left normal after 90° Z rotation
            REQUIRE(isVec3Equal(transformed.normal, glm::vec3(-1.0f, 0.0f, 0.0f)));
            REQUIRE(transformed.distance == Approx(0.0f));
        }

        SECTION("Uniform scaling") {
            glm::mat4 scale = glm::scale(glm::mat4(1.0f), glm::vec3(2.0f, 2.0f, 2.0f));
            Plane planeAtY1(glm::vec3(0.0f, 1.0f, 0.0f), 1.0f); // Plane at Y=1
            Plane transformed = transformPlane(planeAtY1, scale);

            REQUIRE(isVec3Equal(transformed.normal, planeAtY1.normal)); // Normal unchanged
            REQUIRE(transformed.distance == Approx(2.0f)); // Distance scaled
        }
    }

    SECTION("Sphere transformation") {
        Sphere originalSphere(glm::vec3(1.0f, 2.0f, 3.0f), 2.0f);

        SECTION("Translation") {
            glm::mat4 translation = glm::translate(glm::mat4(1.0f), glm::vec3(5.0f, -3.0f, 1.0f));
            Sphere transformed = transformSphere(originalSphere, translation);

            REQUIRE(isVec3Equal(transformed.center, glm::vec3(6.0f, -1.0f, 4.0f)));
            REQUIRE(transformed.radius == Approx(originalSphere.radius)); // Radius unchanged
        }

        SECTION("Uniform scaling") {
            glm::mat4 scale = glm::scale(glm::mat4(1.0f), glm::vec3(3.0f, 3.0f, 3.0f));
            Sphere transformed = transformSphere(originalSphere, scale);

            REQUIRE(isVec3Equal(transformed.center, glm::vec3(3.0f, 6.0f, 9.0f)));
            REQUIRE(transformed.radius == Approx(6.0f)); // Radius scaled by 3
        }

        SECTION("Non-uniform scaling") {
            glm::mat4 scale = glm::scale(glm::mat4(1.0f), glm::vec3(2.0f, 4.0f, 1.0f));
            Sphere transformed = transformSphere(originalSphere, scale);

            REQUIRE(isVec3Equal(transformed.center, glm::vec3(2.0f, 8.0f, 3.0f)));
            REQUIRE(transformed.radius == Approx(8.0f)); // Radius scaled by max scale factor (4)
        }

        SECTION("Rotation") {
            glm::mat4 rotation = glm::rotate(glm::mat4(1.0f), glm::radians(90.0f), glm::vec3(0.0f, 0.0f, 1.0f));
            Sphere transformed = transformSphere(originalSphere, rotation);

            // (1,2,3) rotated 90° around Z becomes (-2,1,3)
            REQUIRE(isVec3Equal(transformed.center, glm::vec3(-2.0f, 1.0f, 3.0f)));
            REQUIRE(transformed.radius == Approx(originalSphere.radius)); // Radius unchanged
        }
    }

    SECTION("AABB transformation") {
        AABB originalAABB(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));

        SECTION("Translation") {
            glm::mat4 translation = glm::translate(glm::mat4(1.0f), glm::vec3(5.0f, -2.0f, 3.0f));
            AABB transformed = transformAABB(originalAABB, translation);

            REQUIRE(isVec3Equal(transformed.min, glm::vec3(4.0f, -3.0f, 2.0f)));
            REQUIRE(isVec3Equal(transformed.max, glm::vec3(6.0f, -1.0f, 4.0f)));
        }

        SECTION("Uniform scaling") {
            glm::mat4 scale = glm::scale(glm::mat4(1.0f), glm::vec3(2.0f, 2.0f, 2.0f));
            AABB transformed = transformAABB(originalAABB, scale);

            REQUIRE(isVec3Equal(transformed.min, glm::vec3(-2.0f, -2.0f, -2.0f)));
            REQUIRE(isVec3Equal(transformed.max, glm::vec3(2.0f, 2.0f, 2.0f)));
        }

        SECTION("Non-uniform scaling") {
            glm::mat4 scale = glm::scale(glm::mat4(1.0f), glm::vec3(3.0f, 1.0f, 2.0f));
            AABB transformed = transformAABB(originalAABB, scale);

            REQUIRE(isVec3Equal(transformed.min, glm::vec3(-3.0f, -1.0f, -2.0f)));
            REQUIRE(isVec3Equal(transformed.max, glm::vec3(3.0f, 1.0f, 2.0f)));
        }

        SECTION("Rotation") {
            glm::mat4 rotation = glm::rotate(glm::mat4(1.0f), glm::radians(45.0f), glm::vec3(0.0f, 0.0f, 1.0f));
            AABB transformed = transformAABB(originalAABB, rotation);

            // After 45° rotation, the AABB should expand to contain all rotated corners
            float expectedSize = glm::sqrt(2.0f) * 2.0f; // Diagonal of original square
            REQUIRE(isVec3Equal(transformed.min, glm::vec3(-expectedSize/2, -expectedSize/2, -1.0f), 0.01f));
            REQUIRE(isVec3Equal(transformed.max, glm::vec3(expectedSize/2, expectedSize/2, 1.0f), 0.01f));
        }
    }
}

TEST_CASE("AABB utility functions", "[utils][collision_math][aabb][utilities]") {
    SECTION("AABB merging") {
        AABB aabb1(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));
        AABB aabb2(glm::vec3(0.0f, 0.0f, 0.0f), glm::vec3(3.0f, 2.0f, 4.0f));

        AABB merged = mergeAABB(aabb1, aabb2);

        REQUIRE(isVec3Equal(merged.min, glm::vec3(-1.0f, -1.0f, -1.0f)));
        REQUIRE(isVec3Equal(merged.max, glm::vec3(3.0f, 2.0f, 4.0f)));

        // Test symmetry
        AABB mergedReverse = mergeAABB(aabb2, aabb1);
        REQUIRE(merged == mergedReverse);
    }

    SECTION("AABB subdivision") {
        AABB parentAABB(glm::vec3(0.0f, 0.0f, 0.0f), glm::vec3(4.0f, 4.0f, 4.0f));
        auto children = subdivideAABB(parentAABB);

        REQUIRE(children.size() == 8);

        // Check that all children have the correct size (half of parent)
        glm::vec3 expectedSize = glm::vec3(2.0f, 2.0f, 2.0f);
        for (const auto& child : children) {
            REQUIRE(isVec3Equal(child.getSize(), expectedSize));
        }

        // Check specific children positions
        REQUIRE(isVec3Equal(children[0].min, glm::vec3(0.0f, 0.0f, 0.0f))); // Bottom-left-front
        REQUIRE(isVec3Equal(children[0].max, glm::vec3(2.0f, 2.0f, 2.0f)));

        REQUIRE(isVec3Equal(children[7].min, glm::vec3(2.0f, 2.0f, 2.0f))); // Top-right-back
        REQUIRE(isVec3Equal(children[7].max, glm::vec3(4.0f, 4.0f, 4.0f)));

        // Verify that all children together cover the parent AABB
        AABB reconstructed = children[0];
        for (size_t i = 1; i < children.size(); ++i) {
            reconstructed = mergeAABB(reconstructed, children[i]);
        }
        REQUIRE(reconstructed == parentAABB);
    }

    SECTION("AABB contains") {
        AABB parent(glm::vec3(-2.0f, -2.0f, -2.0f), glm::vec3(2.0f, 2.0f, 2.0f));
        AABB child(glm::vec3(-1.0f, -1.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f));
        AABB overlapping(glm::vec3(0.0f, 0.0f, 0.0f), glm::vec3(3.0f, 3.0f, 3.0f));
        AABB separate(glm::vec3(5.0f, 5.0f, 5.0f), glm::vec3(6.0f, 6.0f, 6.0f));

        REQUIRE(parent.contains(child));
        REQUIRE_FALSE(parent.contains(overlapping));
        REQUIRE_FALSE(parent.contains(separate));
        REQUIRE_FALSE(child.contains(parent));
    }
}

TEST_CASE("Plane-plane intersection tests", "[utils][collision_math][plane][plane][intersection]") {
    SECTION("Perpendicular planes") {
        Plane planeXZ(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // XZ plane at Y=0
        Plane planeYZ(glm::vec3(1.0f, 0.0f, 0.0f), 0.0f); // YZ plane at X=0

        REQUIRE(planePlaneIntersection(planeXZ, planeYZ));
        REQUIRE(planePlaneIntersection(planeYZ, planeXZ)); // Should be symmetric
    }

    SECTION("Parallel planes - different") {
        Plane plane1(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // XZ plane at Y=0
        Plane plane2(glm::vec3(0.0f, 1.0f, 0.0f), 5.0f); // XZ plane at Y=5

        REQUIRE_FALSE(planePlaneIntersection(plane1, plane2));
        REQUIRE_FALSE(planePlaneIntersection(plane2, plane1)); // Should be symmetric
    }

    SECTION("Parallel planes - same") {
        Plane plane1(glm::vec3(0.0f, 1.0f, 0.0f), 3.0f);
        Plane plane2(glm::vec3(0.0f, 1.0f, 0.0f), 3.0f);

        REQUIRE(planePlaneIntersection(plane1, plane2));
    }

    SECTION("Nearly parallel planes") {
        Plane plane1(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f);
        Plane plane2(glm::vec3(1e-8f, 1.0f, 0.0f), 0.0f); // Very small X component

        // Should be treated as parallel due to epsilon tolerance
        REQUIRE(planePlaneIntersection(plane1, plane2));
    }

    SECTION("Angled planes") {
        Plane plane1(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // Horizontal
        Plane plane2(glm::vec3(1.0f, 1.0f, 0.0f), 0.0f); // 45-degree angle

        REQUIRE(planePlaneIntersection(plane1, plane2));
    }
}

TEST_CASE("AABB-plane collision resolution", "[utils][collision_math][aabb][plane][resolution]") {
    SECTION("AABB penetrating plane from below") {
        Plane horizontalPlane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // XZ plane at Y=0
        AABB aabb(glm::vec3(-1.0f, -2.0f, -1.0f), glm::vec3(1.0f, 1.0f, 1.0f)); // Penetrating plane

        glm::vec3 resolved = resolveAABBPlaneCollision(aabb, horizontalPlane);

        // Should move AABB center so that the AABB doesn't penetrate the plane
        // The AABB extends from Y=-2 to Y=1, so center is at Y=-0.5
        // The AABB extends 1.5 units above its center, so it should be moved to Y=1.5 + epsilon
        REQUIRE(resolved.y > 1.5f); // Should be above the plane
        REQUIRE(isFloatEqual(resolved.x, aabb.getCenter().x)); // X unchanged
        REQUIRE(isFloatEqual(resolved.z, aabb.getCenter().z)); // Z unchanged
    }

    SECTION("AABB not penetrating plane") {
        Plane horizontalPlane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // XZ plane at Y=0
        AABB aabb(glm::vec3(-1.0f, 2.0f, -1.0f), glm::vec3(1.0f, 4.0f, 1.0f)); // Above plane

        glm::vec3 resolved = resolveAABBPlaneCollision(aabb, horizontalPlane);

        // Should remain unchanged
        REQUIRE(isVec3Equal(resolved, aabb.getCenter()));
    }

    SECTION("AABB touching plane") {
        Plane horizontalPlane(glm::vec3(0.0f, 1.0f, 0.0f), 0.0f); // XZ plane at Y=0
        AABB aabb(glm::vec3(-1.0f, 0.0f, -1.0f), glm::vec3(1.0f, 2.0f, 1.0f)); // Bottom edge on plane

        glm::vec3 resolved = resolveAABBPlaneCollision(aabb, horizontalPlane);

        // AABB just touching the plane should remain unchanged (no penetration)
        REQUIRE(isVec3Equal(resolved, aabb.getCenter()));
    }

    SECTION("AABB with angled plane") {
        Plane angledPlane(glm::normalize(glm::vec3(1.0f, 1.0f, 0.0f)), 0.0f); // 45-degree plane
        AABB aabb(glm::vec3(-0.5f, -0.5f, -1.0f), glm::vec3(0.5f, 0.5f, 1.0f)); // Penetrating angled plane

        glm::vec3 resolved = resolveAABBPlaneCollision(aabb, angledPlane);

        // Should be moved away from the plane
        REQUIRE_FALSE(isVec3Equal(resolved, aabb.getCenter()));
    }
}

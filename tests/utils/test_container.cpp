#include <catch2/catch_test_macros.hpp>
#include <catch2/catch_approx.hpp>

#include <unordered_set>

#include "utils/container.hpp"
#include "../test_utils.hpp"

using namespace IronFrost;

// Test classes for container testing
class NonCopyableTestClass {
public:
    int value;
    
    NonCopyableTestClass(int v) : value(v) {}
    
    // Make it non-copyable
    NonCopyableTestClass(const NonCopyableTestClass&) = delete;
    NonCopyableTestClass& operator=(const NonCopyableTestClass&) = delete;
    
    // Allow move
    NonCopyableTestClass(NonCopyableTestClass&&) = default;
    NonCopyableTestClass& operator=(NonCopyableTestClass&&) = default;
    
    bool operator==(const NonCopyableTestClass& other) const {
        return value == other.value;
    }
};

class CopyableTestClass {
public:
    int value;
    
    CopyableTestClass(int v) : value(v) {}
    
    bool operator==(const CopyableTestClass& other) const {
        return value == other.value;
    }
};

// Hash specialization for CopyableTestClass to use in ValueContainer reverse lookup
namespace std {
    template<>
    struct hash<CopyableTestClass> {
        size_t operator()(const CopyableTestClass& obj) const {
            return hash<int>()(obj.value);
        }
    };
}

TEST_CASE("PointerContainer basic operations", "[utils][container][pointer]") {
    PointerContainer<NonCopyableTestClass> container;
    StringID id1 = StringID("test1");
    StringID id2 = StringID("test2");
    
    SECTION("Initial state") {
        REQUIRE(container.size() == 0);
        REQUIRE_FALSE(container.has(id1));
        REQUIRE(container.tryGet(id1) == nullptr);
    }
    
    SECTION("Insert and retrieve") {
        auto obj1 = std::make_unique<NonCopyableTestClass>(42);
        NonCopyableTestClass* ptr1 = obj1.get();
        
        container.insert(id1, std::move(obj1));
        
        REQUIRE(container.size() == 1);
        REQUIRE(container.has(id1));
        REQUIRE(container.get(id1).value == 42);
        REQUIRE(container.tryGet(id1) == ptr1);
        REQUIRE(container.tryGet(id1)->value == 42);
    }
    
    SECTION("Multiple insertions") {
        auto obj1 = std::make_unique<NonCopyableTestClass>(42);
        auto obj2 = std::make_unique<NonCopyableTestClass>(84);
        
        container.insert(id1, std::move(obj1));
        container.insert(id2, std::move(obj2));
        
        REQUIRE(container.size() == 2);
        REQUIRE(container.has(id1));
        REQUIRE(container.has(id2));
        REQUIRE(container.get(id1).value == 42);
        REQUIRE(container.get(id2).value == 84);
    }
    
    SECTION("Remove element") {
        auto obj1 = std::make_unique<NonCopyableTestClass>(42);
        container.insert(id1, std::move(obj1));
        
        REQUIRE(container.has(id1));
        REQUIRE(container.size() == 1);
        
        container.remove(id1);
        
        REQUIRE_FALSE(container.has(id1));
        REQUIRE(container.size() == 0);
        REQUIRE(container.tryGet(id1) == nullptr);
    }
    
    SECTION("Remove non-existent element") {
        container.remove(id1); // Should not crash
        REQUIRE(container.size() == 0);
    }
}

TEST_CASE("PointerContainer error handling", "[utils][container][pointer][error]") {
    PointerContainer<NonCopyableTestClass> container;
    StringID id1 = StringID("test1");
    
    SECTION("Get non-existent element throws") {
        REQUIRE_THROWS_AS(container.get(id1), std::runtime_error);
    }
    
    SECTION("Exception message contains ID") {
        try {
            container.get(id1);
            FAIL("Expected exception was not thrown");
        } catch (const std::runtime_error& e) {
            std::string message = e.what();
            REQUIRE(message.find("test1") != std::string::npos);
            REQUIRE(message.find("Container element not found") != std::string::npos);
        }
    }
}

TEST_CASE("PointerContainer reverse lookup", "[utils][container][pointer][reverse]") {
    PointerContainer<NonCopyableTestClass> container;
    StringID id1 = StringID("reverse1");
    StringID id2 = StringID("reverse2");
    
    auto obj1 = std::make_unique<NonCopyableTestClass>(100);
    auto obj2 = std::make_unique<NonCopyableTestClass>(200);
    
    NonCopyableTestClass* ptr1 = obj1.get();
    NonCopyableTestClass* ptr2 = obj2.get();
    
    container.insert(id1, std::move(obj1));
    container.insert(id2, std::move(obj2));
    
    SECTION("Find ID by pointer") {
        auto foundId1 = container.findID(ptr1);
        auto foundId2 = container.findID(ptr2);
        
        REQUIRE(foundId1.has_value());
        REQUIRE(foundId2.has_value());
        REQUIRE(foundId1.value() == id1);
        REQUIRE(foundId2.value() == id2);
    }
    
    SECTION("Find ID for non-existent pointer") {
        NonCopyableTestClass dummy(999);
        auto foundId = container.findID(&dummy);
        
        REQUIRE_FALSE(foundId.has_value());
    }
    
    SECTION("Reverse lookup after removal") {
        container.remove(id1);
        auto foundId = container.findID(ptr1);
        
        REQUIRE_FALSE(foundId.has_value());
    }
}

TEST_CASE("PointerContainer iteration", "[utils][container][pointer][iteration]") {
    PointerContainer<NonCopyableTestClass> container;
    StringID id1 = StringID("iter1");
    StringID id2 = StringID("iter2");
    StringID id3 = StringID("iter3");
    
    container.insert(id1, std::make_unique<NonCopyableTestClass>(10));
    container.insert(id2, std::make_unique<NonCopyableTestClass>(20));
    container.insert(id3, std::make_unique<NonCopyableTestClass>(30));
    
    SECTION("Range-based for loop") {
        std::unordered_set<int> values;
        for (const auto& [id, ptr] : container) {
            values.insert(ptr->value);
        }
        
        REQUIRE(values.size() == 3);
        REQUIRE(values.count(10) == 1);
        REQUIRE(values.count(20) == 1);
        REQUIRE(values.count(30) == 1);
    }
    
    SECTION("Iterator-based loop") {
        int count = 0;
        for (auto it = container.begin(); it != container.end(); ++it) {
            REQUIRE(it->second != nullptr);
            count++;
        }
        
        REQUIRE(count == 3);
    }
}

TEST_CASE("ValueContainer basic operations", "[utils][container][value]") {
    ValueContainer<CopyableTestClass> container;
    StringID id1 = StringID("value1");
    StringID id2 = StringID("value2");
    
    SECTION("Initial state") {
        REQUIRE(container.size() == 0);
        REQUIRE_FALSE(container.has(id1));
        REQUIRE(container.tryGet(id1) == nullptr);
    }
    
    SECTION("Insert and retrieve") {
        CopyableTestClass obj1(42);
        container.insert(id1, obj1);
        
        REQUIRE(container.size() == 1);
        REQUIRE(container.has(id1));
        REQUIRE(container.get(id1).value == 42);
        REQUIRE(container.tryGet(id1) != nullptr);
        REQUIRE(container.tryGet(id1)->value == 42);
    }
    
    SECTION("Multiple insertions") {
        CopyableTestClass obj1(42);
        CopyableTestClass obj2(84);
        
        container.insert(id1, obj1);
        container.insert(id2, obj2);
        
        REQUIRE(container.size() == 2);
        REQUIRE(container.has(id1));
        REQUIRE(container.has(id2));
        REQUIRE(container.get(id1).value == 42);
        REQUIRE(container.get(id2).value == 84);
    }
    
    SECTION("Remove element") {
        CopyableTestClass obj1(42);
        container.insert(id1, obj1);
        
        REQUIRE(container.has(id1));
        REQUIRE(container.size() == 1);
        
        container.remove(id1);
        
        REQUIRE_FALSE(container.has(id1));
        REQUIRE(container.size() == 0);
        REQUIRE(container.tryGet(id1) == nullptr);
    }
}

TEST_CASE("ValueContainer reverse lookup", "[utils][container][value][reverse]") {
    ValueContainer<CopyableTestClass> container;
    StringID id1 = StringID("vreverse1");
    StringID id2 = StringID("vreverse2");
    
    CopyableTestClass obj1(100);
    CopyableTestClass obj2(200);
    
    container.insert(id1, obj1);
    container.insert(id2, obj2);
    
    SECTION("Find ID by value") {
        auto foundId1 = container.findID(obj1);
        auto foundId2 = container.findID(obj2);
        
        REQUIRE(foundId1.has_value());
        REQUIRE(foundId2.has_value());
        REQUIRE(foundId1.value() == id1);
        REQUIRE(foundId2.value() == id2);
    }
    
    SECTION("Find ID for non-existent value") {
        CopyableTestClass dummy(999);
        auto foundId = container.findID(dummy);
        
        REQUIRE_FALSE(foundId.has_value());
    }
}

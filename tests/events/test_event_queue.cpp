#include <catch2/catch_test_macros.hpp>

#include "events/event_queue.hpp"
#include "events/events.hpp"

#include <atomic>
#include <chrono>
#include <memory>
#include <thread>
#include <vector>

using namespace IronFrost;

// Test event for testing purposes
class TestEvent : public Event {
private:
    std::string message_;
    int value_;
    
public:
    TestEvent(const std::string& message, int value) : message_(message), value_(value) {}
    
    std::string toString() const override {
        return "TestEvent: " + message_;
    }
    
    const std::string& getMessage() const { return message_; }
    int getValue() const { return value_; }
};

TEST_CASE("EventQueue basic functionality", "[events][event_queue][basic]") {
    EventQueue queue;
    
    SECTION("Initially empty") {
        REQUIRE(queue.empty());
    }
    
    SECTION("Push and pop single event") {
        auto event = std::make_shared<TestEvent>("test", 42);
        
        queue.push(event);
        REQUIRE_FALSE(queue.empty());
        
        auto retrieved = queue.pop();
        REQUIRE(queue.empty());
        
        // Verify it's the same event
        REQUIRE(retrieved == event);
        
        auto testEvent = std::dynamic_pointer_cast<TestEvent>(retrieved);
        REQUIRE(testEvent != nullptr);
        REQUIRE(testEvent->getMessage() == "test");
        REQUIRE(testEvent->getValue() == 42);
    }
    
    SECTION("Push multiple events, pop in order") {
        auto event1 = std::make_shared<TestEvent>("first", 1);
        auto event2 = std::make_shared<TestEvent>("second", 2);
        auto event3 = std::make_shared<TestEvent>("third", 3);
        
        queue.push(event1);
        queue.push(event2);
        queue.push(event3);
        
        REQUIRE_FALSE(queue.empty());
        
        // Pop in FIFO order
        auto retrieved1 = queue.pop();
        auto retrieved2 = queue.pop();
        auto retrieved3 = queue.pop();
        
        REQUIRE(queue.empty());
        
        // Verify order and content
        auto test1 = std::dynamic_pointer_cast<TestEvent>(retrieved1);
        auto test2 = std::dynamic_pointer_cast<TestEvent>(retrieved2);
        auto test3 = std::dynamic_pointer_cast<TestEvent>(retrieved3);
        
        REQUIRE(test1->getMessage() == "first");
        REQUIRE(test2->getMessage() == "second");
        REQUIRE(test3->getMessage() == "third");
        
        REQUIRE(test1->getValue() == 1);
        REQUIRE(test2->getValue() == 2);
        REQUIRE(test3->getValue() == 3);
    }
}

TEST_CASE("EventQueue with different event types", "[events][event_queue][types]") {
    EventQueue queue;
    
    SECTION("Mix of different event types") {
        auto keyEvent = std::make_shared<KeyDownEvent>(65); // 'A' key
        auto sceneEvent = std::make_shared<SceneUpdateEvent>();
        auto consoleEvent = std::make_shared<ConsoleCommandEvent>("help");
        auto windowEvent = std::make_shared<WindowResizeEvent>(1920.0f, 1080.0f);
        
        queue.push(keyEvent);
        queue.push(sceneEvent);
        queue.push(consoleEvent);
        queue.push(windowEvent);
        
        // Pop and verify each event type
        auto retrieved1 = queue.pop();
        auto retrieved2 = queue.pop();
        auto retrieved3 = queue.pop();
        auto retrieved4 = queue.pop();
        
        REQUIRE(queue.empty());
        
        // Verify types and content
        auto keyDown = std::dynamic_pointer_cast<KeyDownEvent>(retrieved1);
        REQUIRE(keyDown != nullptr);
        REQUIRE(keyDown->getKey() == 65);
        REQUIRE(keyDown->toString() == "KeyDownEvent");
        
        auto sceneUpdate = std::dynamic_pointer_cast<SceneUpdateEvent>(retrieved2);
        REQUIRE(sceneUpdate != nullptr);
        REQUIRE(sceneUpdate->toString() == "SceneUpdateEvent");
        
        auto consoleCmd = std::dynamic_pointer_cast<ConsoleCommandEvent>(retrieved3);
        REQUIRE(consoleCmd != nullptr);
        REQUIRE(consoleCmd->getCommand() == "help");
        REQUIRE(consoleCmd->toString() == "ConsoleCommandEvent");
        
        auto windowResize = std::dynamic_pointer_cast<WindowResizeEvent>(retrieved4);
        REQUIRE(windowResize != nullptr);
        REQUIRE(windowResize->getWidth() == 1920.0f);
        REQUIRE(windowResize->getHeight() == 1080.0f);
        REQUIRE(windowResize->toString() == "WindowResizeEvent");
    }
}

TEST_CASE("EventQueue thread safety", "[events][event_queue][threading]") {
    EventQueue queue;
    
    SECTION("Single producer, single consumer") {
        const int numEvents = 100;
        std::atomic<int> consumedCount{0};
        std::vector<int> consumedValues;
        std::mutex consumedMutex;
        
        // Producer thread
        std::thread producer([&queue, numEvents]() {
            for (int i = 0; i < numEvents; ++i) {
                auto event = std::make_shared<TestEvent>("event_" + std::to_string(i), i);
                queue.push(event);
                
                // Small delay to make threading more realistic
                std::this_thread::sleep_for(std::chrono::microseconds(10));
            }
        });
        
        // Consumer thread
        std::thread consumer([&queue, &consumedCount, &consumedValues, &consumedMutex, numEvents]() {
            while (consumedCount.load() < numEvents) {
                auto event = queue.pop();
                auto testEvent = std::dynamic_pointer_cast<TestEvent>(event);
                
                if (testEvent) {
                    std::lock_guard<std::mutex> lock(consumedMutex);
                    consumedValues.push_back(testEvent->getValue());
                    consumedCount.fetch_add(1);
                }
            }
        });
        
        producer.join();
        consumer.join();
        
        // Verify all events were consumed
        REQUIRE(consumedCount.load() == numEvents);
        REQUIRE(consumedValues.size() == numEvents);
        REQUIRE(queue.empty());
        
        // Verify order (should be 0, 1, 2, ..., 99)
        for (int i = 0; i < numEvents; ++i) {
            REQUIRE(consumedValues[i] == i);
        }
    }
    
    SECTION("Multiple producers, single consumer") {
        const int numProducers = 5;
        const int eventsPerProducer = 20;
        const int totalEvents = numProducers * eventsPerProducer;
        
        std::atomic<int> consumedCount{0};
        std::vector<std::string> consumedMessages;
        std::mutex consumedMutex;
        
        // Multiple producer threads
        std::vector<std::thread> producers;
        for (int p = 0; p < numProducers; ++p) {
            producers.emplace_back([&queue, p, eventsPerProducer]() {
                for (int i = 0; i < eventsPerProducer; ++i) {
                    std::string message = "producer_" + std::to_string(p) + "_event_" + std::to_string(i);
                    auto event = std::make_shared<TestEvent>(message, p * 1000 + i);
                    queue.push(event);
                    
                    std::this_thread::sleep_for(std::chrono::microseconds(5));
                }
            });
        }
        
        // Single consumer thread
        std::thread consumer([&queue, &consumedCount, &consumedMessages, &consumedMutex, totalEvents]() {
            while (consumedCount.load() < totalEvents) {
                auto event = queue.pop();
                auto testEvent = std::dynamic_pointer_cast<TestEvent>(event);
                
                if (testEvent) {
                    std::lock_guard<std::mutex> lock(consumedMutex);
                    consumedMessages.push_back(testEvent->getMessage());
                    consumedCount.fetch_add(1);
                }
            }
        });
        
        // Wait for all threads
        for (auto& producer : producers) {
            producer.join();
        }
        consumer.join();
        
        // Verify all events were consumed
        REQUIRE(consumedCount.load() == totalEvents);
        REQUIRE(consumedMessages.size() == totalEvents);
        REQUIRE(queue.empty());
        
        // Verify we got events from all producers
        std::vector<int> producerCounts(numProducers, 0);
        for (const auto& message : consumedMessages) {
            for (int p = 0; p < numProducers; ++p) {
                if (message.find("producer_" + std::to_string(p)) != std::string::npos) {
                    producerCounts[p]++;
                    break;
                }
            }
        }
        
        for (int p = 0; p < numProducers; ++p) {
            REQUIRE(producerCounts[p] == eventsPerProducer);
        }
    }
}

TEST_CASE("EventQueue blocking behavior", "[events][event_queue][blocking]") {
    EventQueue queue;
    
    SECTION("Pop blocks until event is available") {
        std::atomic<bool> popStarted{false};
        std::atomic<bool> popCompleted{false};
        std::shared_ptr<Event> retrievedEvent;
        
        // Consumer thread that will block
        std::thread consumer([&queue, &popStarted, &popCompleted, &retrievedEvent]() {
            popStarted.store(true);
            retrievedEvent = queue.pop();
            popCompleted.store(true);
        });
        
        // Wait for consumer to start
        while (!popStarted.load()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
        
        // Give some time to ensure pop() is blocking
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
        REQUIRE_FALSE(popCompleted.load());
        
        // Push an event
        auto event = std::make_shared<TestEvent>("unblock", 123);
        queue.push(event);
        
        // Consumer should complete quickly now
        consumer.join();
        
        REQUIRE(popCompleted.load());
        REQUIRE(retrievedEvent == event);
        
        auto testEvent = std::dynamic_pointer_cast<TestEvent>(retrievedEvent);
        REQUIRE(testEvent->getMessage() == "unblock");
        REQUIRE(testEvent->getValue() == 123);
    }
}

// Concrete implementation of EventWithCallback for testing
class TestCallbackEvent : public EventWithCallback {
public:
    explicit TestCallbackEvent(std::optional<std::function<void()>> callback = std::nullopt)
        : EventWithCallback(callback) {}

    std::string toString() const override {
        return "TestCallbackEvent";
    }
};

TEST_CASE("EventQueue with EventWithCallback", "[events][event_queue][callbacks]") {
    EventQueue queue;

    SECTION("Event with callback execution") {
        std::atomic<bool> callbackExecuted{false};
        std::string callbackMessage;

        auto callback = [&callbackExecuted, &callbackMessage]() {
            callbackExecuted.store(true);
            callbackMessage = "Callback executed!";
        };

        auto event = std::make_shared<TestCallbackEvent>(callback);

        queue.push(event);
        auto retrieved = queue.pop();

        // Execute the callback
        auto callbackEvent = std::dynamic_pointer_cast<TestCallbackEvent>(retrieved);
        REQUIRE(callbackEvent != nullptr);

        REQUIRE_FALSE(callbackExecuted.load());
        callbackEvent->callCallback();
        REQUIRE(callbackExecuted.load());
        REQUIRE(callbackMessage == "Callback executed!");
    }

    SECTION("Event without callback") {
        auto event = std::make_shared<TestCallbackEvent>(std::nullopt);

        queue.push(event);
        auto retrieved = queue.pop();

        auto callbackEvent = std::dynamic_pointer_cast<TestCallbackEvent>(retrieved);
        REQUIRE(callbackEvent != nullptr);

        // Should not crash when calling callback on event without callback
        callbackEvent->callCallback(); // Should do nothing
    }
}

TEST_CASE("EventQueue stress test", "[events][event_queue][stress]") {
    EventQueue queue;

    SECTION("High volume event processing") {
        const int numEvents = 1000;
        std::atomic<int> processedCount{0};

        // Producer thread pushing many events rapidly
        std::thread producer([&queue, numEvents]() {
            for (int i = 0; i < numEvents; ++i) {
                auto event = std::make_shared<KeyDownEvent>(i % 256);
                queue.push(event);
            }
        });

        // Consumer thread processing events
        std::thread consumer([&queue, &processedCount, numEvents]() {
            while (processedCount.load() < numEvents) {
                auto event = queue.pop();
                auto keyEvent = std::dynamic_pointer_cast<KeyDownEvent>(event);

                if (keyEvent) {
                    // Simulate some processing
                    int key = keyEvent->getKey();
                    REQUIRE(key >= 0);
                    REQUIRE(key < 256);

                    processedCount.fetch_add(1);
                }
            }
        });

        producer.join();
        consumer.join();

        REQUIRE(processedCount.load() == numEvents);
        REQUIRE(queue.empty());
    }
}

TEST_CASE("EventQueue memory management", "[events][event_queue][memory]") {
    EventQueue queue;

    SECTION("Shared pointer reference counting") {
        std::shared_ptr<TestEvent> event;

        {
            // Create event in inner scope
            auto localEvent = std::make_shared<TestEvent>("memory_test", 999);
            event = localEvent; // Keep a reference

            queue.push(localEvent);
            // localEvent goes out of scope, but event should still be valid
        }

        // Event should still be accessible through our reference
        REQUIRE(event->getMessage() == "memory_test");
        REQUIRE(event->getValue() == 999);

        // Pop from queue
        auto retrieved = queue.pop();
        auto testEvent = std::dynamic_pointer_cast<TestEvent>(retrieved);

        REQUIRE(testEvent != nullptr);
        REQUIRE(testEvent->getMessage() == "memory_test");
        REQUIRE(testEvent->getValue() == 999);

        // Should be the same object
        REQUIRE(testEvent == event);
    }

    SECTION("Event cleanup after processing") {
        std::weak_ptr<TestEvent> weakRef;

        {
            auto event = std::make_shared<TestEvent>("cleanup_test", 777);
            weakRef = event;

            queue.push(event);
            // event goes out of scope
        }

        // Event should still exist (held by queue)
        REQUIRE_FALSE(weakRef.expired());

        auto retrieved = queue.pop();
        // Event should still exist (held by retrieved)
        REQUIRE_FALSE(weakRef.expired());

        retrieved.reset();
        // Now event should be cleaned up
        REQUIRE(weakRef.expired());
    }
}

#ifndef __IF__COLLISION_MATH_AABB_HPP
#define __IF__COLLISION_MATH_AABB_HPP

// Third-party libraries
#include <glm/glm.hpp>

namespace IronFrost {
  namespace CollisionMath {
    /**
     * Axis-Aligned Bounding Box
     */
    struct AABB {
      glm::vec3 min;
      glm::vec3 max;

      // Default constructor
      AABB() : min(0.0f), max(0.0f) {}

      AABB(const glm::vec3& min, const glm::vec3& max) : min(min), max(max) {}

      // Create AABB from center and size
      static AABB fromCenterAndSize(const glm::vec3& center, const glm::vec3& size) {
        glm::vec3 halfSize = size * 0.5f;
        return AABB(center - halfSize, center + halfSize);
      }

      glm::vec3 getCenter() const {
        return (min + max) * 0.5f;
      }

      glm::vec3 getSize() const {
        return max - min;
      }

      bool contains(const AABB& other) const {
        return min.x <= other.min.x && max.x >= other.max.x &&
               min.y <= other.min.y && max.y >= other.max.y &&
               min.z <= other.min.z && max.z >= other.max.z;
      }

      bool operator==(const AABB& other) const {
        return min == other.min && max == other.max;
      }
    };

    // ============================================================================
    // AABB FUNCTIONS
    // ============================================================================

    /**
     * Check if a point is inside an AABB
     */
    inline bool isPointInAABB(const glm::vec3& point, const AABB& aabb) {
      return point.x >= aabb.min.x && point.x <= aabb.max.x &&
             point.y >= aabb.min.y && point.y <= aabb.max.y &&
             point.z >= aabb.min.z && point.z <= aabb.max.z;
    }

    /**
     * Check if two AABBs intersect
     */
    inline bool aabbIntersection(const AABB& a, const AABB& b) {
      return a.min.x <= b.max.x && a.max.x >= b.min.x &&
             a.min.y <= b.max.y && a.max.y >= b.min.y &&
             a.min.z <= b.max.z && a.max.z >= b.min.z;
    }

    /**
     * Get the closest point on an AABB to a given point
     */
    inline glm::vec3 closestPointOnAABB(const glm::vec3& point, const AABB& aabb) {
      return glm::vec3(
        glm::clamp(point.x, aabb.min.x, aabb.max.x),
        glm::clamp(point.y, aabb.min.y, aabb.max.y),
        glm::clamp(point.z, aabb.min.z, aabb.max.z)
      );
    }

    /**
     * Calculate the distance from a point to an AABB
     * Returns 0 if the point is inside the AABB
     */
    inline float distanceToAABB(const glm::vec3& point, const AABB& aabb) {
      glm::vec3 closestPoint = closestPointOnAABB(point, aabb);
      return glm::length(point - closestPoint);
    }

    inline AABB mergeAABB(const AABB& a, const AABB& b) {
      return AABB(glm::min(a.min, b.min), glm::max(a.max, b.max));
    }

    /**
     * Transform an AABB by a transformation matrix
     * Returns a new AABB that encompasses all transformed corners
     */
    inline AABB transformAABB(const AABB& aabb, const glm::mat4& transform) {
      glm::vec3 corners[8] = {
        {aabb.min.x, aabb.min.y, aabb.min.z},
        {aabb.max.x, aabb.min.y, aabb.min.z},
        {aabb.min.x, aabb.max.y, aabb.min.z},
        {aabb.max.x, aabb.max.y, aabb.min.z},
        {aabb.min.x, aabb.min.y, aabb.max.z},
        {aabb.max.x, aabb.min.y, aabb.max.z},
        {aabb.min.x, aabb.max.y, aabb.max.z},
        {aabb.max.x, aabb.max.y, aabb.max.z},
      };

      glm::vec3 min(FLT_MAX);
      glm::vec3 max(-FLT_MAX);

      for (const auto& corner : corners) {
        glm::vec3 transformed = glm::vec3(transform * glm::vec4(corner, 1.0f));
        min = glm::min(min, transformed);
        max = glm::max(max, transformed);
      }

      return AABB(min, max);
    }

    /**
     * Subdivide an AABB into 8 smaller AABBs (octree subdivision)
     */
    inline std::array<AABB, 8> subdivideAABB(const AABB& aabb) {
      std::array<AABB, 8> children;
      glm::vec3 center = aabb.getCenter();
      glm::vec3 size = (aabb.max - aabb.min) * 0.5f;

      glm::vec3 min = aabb.min;

      int i = 0;
      for (int x = 0; x <= 1; ++x) {
        for (int y = 0; y <= 1; ++y) {
          for (int z = 0; z <= 1; ++z) {
            glm::vec3 offset = glm::vec3(x, y, z) * size;
            glm::vec3 childMin = min + offset;
            glm::vec3 childMax = childMin + size;
            children[i++] = AABB(childMin, childMax);
          }
        }
      }

      return children;
    }
  }
}

namespace std {
  template <>
  struct hash<IronFrost::CollisionMath::AABB> {
    size_t operator()(const IronFrost::CollisionMath::AABB& aabb) const {
      size_t seed = 0;
      hash_combine(seed, aabb.min.x);
      hash_combine(seed, aabb.min.y);
      hash_combine(seed, aabb.min.z);
      hash_combine(seed, aabb.max.x);
      hash_combine(seed, aabb.max.y);
      hash_combine(seed, aabb.max.z);
      return seed;
    }
  };
}

#endif

#ifndef __IF__CONTAINERS_SET_HPP
#define __IF__CONTAINERS_SET_HPP

// C++ standard library
#include <unordered_set>
#include <memory>

// Local includes
#include "string_id.hpp"
#include "container.hpp"

namespace IronFrost { 
  template<template<typename> class ContainerType, typename... Types>
  class ContainersSet {
    public:
      using ContainersTuple = std::tuple<ContainerType<Types>...>;

      template<typename T>
      ContainerType<T>& getContainer() {
        return std::get<ContainerType<T>>(m_containers);
      }

      template<typename T>
      const ContainerType<T>& getContainer() const {
        return std::get<ContainerType<T>>(m_containers);
      }

    private:
      ContainersTuple m_containers;
  };

  template<typename... Types>
  using ValueContainersSet = ContainersSet<ValueContainer, Types...>;

  template<typename... Types>
  using PointerContainersSet = ContainersSet<PointerContainer, Types...>;
}

#endif

#ifndef __IF__CONTAINER_HPP
#define __IF__CONTAINER_HPP

// C++ standard library
#include <unordered_map>
#include <memory>

// Local includes
#include "string_id.hpp"

namespace IronFrost {
  template<typename T>
  class PointerContainer {
    static_assert(!std::is_copy_constructible_v<T>, "T must not be copyable");

    private:
      std::unordered_map<StringID, std::unique_ptr<T>> m_container;
      std::unordered_map<T*, StringID> m_reverseLookup;
    
    public:
      void insert(const StringID& id, std::unique_ptr<T> element) {
        T* ptr = element.get();
        m_reverseLookup[ptr] = id;
        m_container.try_emplace(id, std::move(element));
      }

      void remove(const StringID& id) {
        auto it = m_container.find(id);
        if (it != m_container.end()) {
          m_reverseLookup.erase(it->second.get());
          m_container.erase(id);
        }
      }

      const T& get(const StringID& id) const {
        auto it = m_container.find(id);
        if (it == m_container.end()) {
          throw std::runtime_error("Container element not found: " + StringID::getString(id) + " in " + typeid(T).name());
        }
        return *it->second;
      }

      T& get(const StringID& id) {
        auto it = m_container.find(id);
        if (it == m_container.end()) {
          throw std::runtime_error("Container element not found: " + StringID::getString(id) + " in " + typeid(T).name());
        }
        return *it->second;
      }

      T* tryGet(const StringID& id) {
        auto it = m_container.find(id);
        if (it == m_container.end()) {
          return nullptr;
        }
        return it->second.get();
      }

      bool has(const StringID& id) const {
        return m_container.find(id) != m_container.end();
      }

      size_t size() const {
        return m_container.size();
      }

      std::optional<StringID> findID(const T* ptr) const {
        auto it = m_reverseLookup.find(const_cast<T*>(ptr));
        if (it == m_reverseLookup.end()) {
          return std::nullopt;
        }
        return it->second;
      }

      auto begin() { return m_container.begin(); }
      auto end() { return m_container.end(); }
  };

  template<typename T>
  class ValueContainer {
    private:
      std::unordered_map<StringID, T> m_container;
      std::unordered_map<T, StringID> m_reverseLookup;
    
    public:
      void insert(const StringID& id, const T& element) {
        m_reverseLookup[element] = id;
        m_container.try_emplace(id, std::move(element));
      }

      void remove(const StringID& id) {
        auto it = m_container.find(id);
        if (it != m_container.end()) {
          m_reverseLookup.erase(it->second);
          m_container.erase(id);
        }
      }

      const T& get(const StringID& id) const {
        auto it = m_container.find(id);
        if (it == m_container.end()) {
          throw std::runtime_error("Container element not found: " + StringID::getString(id) + " in " + typeid(T).name());
        }
        return it->second;
      }

      T& get(const StringID& id) {
        auto it = m_container.find(id);
        if (it == m_container.end()) {
          throw std::runtime_error("Container element not found: " + StringID::getString(id) + " in " + typeid(T).name());
        }
        return it->second;
      }

      T* tryGet(const StringID& id) {
        auto it = m_container.find(id);
        if (it == m_container.end()) {
          return nullptr;
        }
        return &it->second;
      }

      bool has(const StringID& id) const {
        return m_container.find(id) != m_container.end();
      }

      size_t size() const {
        return m_container.size();
      }

      std::optional<StringID> findID(const T& element) const {
        auto it = m_reverseLookup.find(element);
        if (it == m_reverseLookup.end()) {
          return std::nullopt;
        }
        return it->second;
      }

      auto begin() { return m_container.begin(); }
      auto end() { return m_container.end(); }
  };
}

#endif

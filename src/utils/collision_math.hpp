#ifndef __IF__COLLISION_MATH_HPP
#define __IF__COLLISION_MATH_HPP

// Third-party libraries
#include <glm/glm.hpp>

// C++ standard library
#include <array>
#include <cfloat>
#include <optional>

// Local includes
#include "hash.hpp"

#include "collision_math/plane.hpp"
#include "collision_math/ray.hpp"
#include "collision_math/aabb.hpp"
#include "collision_math/sphere.hpp"

namespace IronFrost {
  namespace CollisionMath {
    /**
     * Ray-plane intersection
     * Returns the parameter t where intersection point = ray.origin + t * ray.direction
     * Returns nullopt if ray is parallel to plane or intersection is behind ray origin
     */
    inline std::optional<float> rayPlaneIntersection(const Ray& ray, const Plane& plane) {
      float denominator = glm::dot(ray.direction, plane.normal);

      // Ray is parallel to plane
      if (std::abs(denominator) < 1e-6f) {
        return std::nullopt;
      }

      float t = (plane.distance - glm::dot(ray.origin, plane.normal)) / denominator;

      // Intersection is behind ray origin
      if (t < 0.0f) {
        return std::nullopt;
      }

      return t;
    }

    /**
     * Check if a sphere intersects with a plane
     * Returns true if the sphere intersects or is below the plane
     */
    inline bool spherePlaneIntersection(const Sphere& sphere, const Plane& plane) {
      float distance = distanceToPlane(sphere.center, plane);
      return distance <= sphere.radius;
    }

    /**
     * Check if an AABB intersects with a plane
     */
    inline bool aabbPlaneIntersection(const AABB& aabb, const Plane& plane) {
      glm::vec3 center   = aabb.getCenter();
      glm::vec3 halfSize = (aabb.max - aabb.min) * 0.5f;

      float d = distanceToPlane(center, plane);
      float r = glm::dot(glm::abs(plane.normal), halfSize);

      return std::abs(d) <= r;
    }

    /**
     * Check if two planes intersect
     * Returns true unless they are parallel and different planes
     */
    inline bool planePlaneIntersection(const Plane& planeA, const Plane& planeB) {
      // Two planes intersect unless they are parallel
      // Check if normals are parallel (cross product is zero)
      glm::vec3 cross = glm::cross(planeA.normal, planeB.normal);
      float crossLength = glm::length(cross);

      // If cross product is very small, planes are parallel
      constexpr float epsilon = 1e-6f;
      if (crossLength < epsilon) {
        // Parallel planes intersect only if they are the same plane
        return std::abs(planeA.distance - planeB.distance) < epsilon;
      }

      // Non-parallel planes always intersect
      return true;
    }

    /**
     * Check if a sphere intersects with an AABB
     */
    inline bool sphereAABBIntersection(const Sphere& sphere, const AABB& aabb) {
      float distance = distanceToAABB(sphere.center, aabb);
      return distance <= sphere.radius;
    }

    /**
     * Get the penetration depth of a sphere into a plane
     * Returns positive value if sphere is penetrating the plane
     */
    inline float spherePlanePenetration(const Sphere& sphere, const Plane& plane) {
      float distance = distanceToPlane(sphere.center, plane);
      if (distance < sphere.radius) {
        return sphere.radius - distance;
      }
      return 0.0f;
    }

    inline float sphereSpherePenetration(const Sphere& sphereA, const Sphere& sphereB) {
      float distance = glm::length(sphereA.center - sphereB.center);
      float radiusSum = sphereA.radius + sphereB.radius;
      return (distance < radiusSum) ? radiusSum - distance : 0.0f;
    }

    inline float sphereAABBPenetration(const Sphere& sphere, const AABB& aabb) {
      float distance = distanceToAABB(sphere.center, aabb);
      return (distance < sphere.radius) ? sphere.radius - distance : 0.0f;
    }

    /**
     * Resolve sphere-plane collision by moving the sphere out of the plane
     * Returns the corrected sphere center position
     */
    inline glm::vec3 resolveSpherePlaneCollision(const Sphere& sphere, const Plane& plane) {
      float penetration = spherePlanePenetration(sphere, plane);
      if (penetration > 0.0f) {
        return sphere.center + plane.normal * penetration;
      }
      return sphere.center;
    }

    /**
     * Resolve AABB-plane collision by moving the AABB out of the plane
     * Returns the corrected AABB center position
     */
    inline glm::vec3 resolveAABBPlaneCollision(const AABB& aabb, const Plane& plane) {
      // Find the closest point on the AABB to the plane
      glm::vec3 center = aabb.getCenter();
      glm::vec3 halfSize = (aabb.max - aabb.min) * 0.5f;

      // Calculate the distance from AABB center to plane
      float centerDistance = distanceToPlane(center, plane);

      // Calculate how far the AABB extends towards the plane
      float aabbExtent = glm::dot(glm::abs(plane.normal), halfSize);

      // If the AABB is penetrating the plane, push it out
      if (centerDistance < aabbExtent) {
        float penetration = aabbExtent - centerDistance;
        return center + plane.normal * (penetration + 1e-4f); // Small epsilon to avoid floating point issues
      }

      return center;
    }

    inline glm::vec3 resolveSphereSphereCollision(const Sphere& sphereA, const Sphere& sphereB) {
      constexpr float eps = 1e-4f;

      glm::vec3 direction = sphereA.center - sphereB.center;
      float distance = glm::length(direction);
      float radiusSum = sphereA.radius + sphereB.radius;

      if (distance >= radiusSum) {
        return sphereA.center;
      }

      if (distance > eps) {
        float penetration = radiusSum - distance;
        return sphereA.center + (direction / distance) * (penetration + eps);
      } else {
        return sphereA.center + glm::vec3(radiusSum + eps, 0.0f, 0.0f);
      }
    }

    inline glm::vec3 resolveSphereAABBCollision(const Sphere& sphere, const AABB& aabb) {
      constexpr float eps = 1e-4f;

      glm::vec3 closest = closestPointOnAABB(sphere.center, aabb);
      glm::vec3 direction = sphere.center - closest;
      float distance = glm::length(direction);

      if (distance >= sphere.radius) {
        return sphere.center;
      }

      if (distance > eps) {
        float penetration = sphere.radius - distance;
        return sphere.center + (direction / distance) * (penetration + eps);
      }

      // Sphere center inside AABB - push to nearest face
      glm::vec3 center = aabb.getCenter();
      glm::vec3 local = sphere.center - center;
      glm::vec3 extent = (aabb.max - aabb.min) * 0.5f;
      glm::vec3 dist = extent - glm::abs(local);

      int axis = (dist.x <= dist.y && dist.x <= dist.z) ? 0 : (dist.y <= dist.z) ? 1 : 2;
      glm::vec3 normal(0.0f);
      normal[axis] = (local[axis] >= 0.0f) ? 1.0f : -1.0f;
      
      return sphere.center + normal * (sphere.radius + eps);
    }

    inline glm::vec3 resolveAABBSphereCollision(const AABB& aabb, const Sphere& sphere) {
      constexpr float eps = 1e-4f;

      glm::vec3 closest = closestPointOnAABB(sphere.center, aabb);
      glm::vec3 direction = closest - sphere.center;
      float distance = glm::length(direction);
      
      glm::vec3 center = aabb.getCenter();
      
      if (distance >= sphere.radius) {
        return center;
      }

      if (distance > eps) {
        float penetration = sphere.radius - distance;
        return center + (direction / distance) * (penetration + eps);
      }

      // Sphere center inside AABB - push along dominant axis
      glm::vec3 local = center - sphere.center;
      glm::vec3 absLocal = glm::abs(local);
      
      int axis = (absLocal.x >= absLocal.y && absLocal.x >= absLocal.z) ? 0 : (absLocal.y >= absLocal.z) ? 1 : 2;
      glm::vec3 normal(0.0f);
      normal[axis] = (local[axis] >= 0.0f) ? 1.0f : -1.0f;
      
      return center + normal * (sphere.radius + eps);
    }

    inline glm::vec3 resolveAABBAABBCollision(const AABB& aabbA, const AABB& aabbB) {
      constexpr float eps = 1e-4f;

      glm::vec3 centerA = aabbA.getCenter();
      glm::vec3 delta = centerA - aabbB.getCenter();
      glm::vec3 halfA = (aabbA.max - aabbA.min) * 0.5f;
      glm::vec3 halfB = (aabbB.max - aabbB.min) * 0.5f;
      glm::vec3 overlap = halfA + halfB - glm::abs(delta);

      if (overlap.x <= 0.0f || overlap.y <= 0.0f || overlap.z <= 0.0f) {
        return centerA;
      }

      int axis = (overlap.x <= overlap.y && overlap.x <= overlap.z) ? 0 : (overlap.y <= overlap.z) ? 1 : 2;
      float sign = (delta[axis] > 0.0f) ? 1.0f : (delta[axis] < 0.0f ? -1.0f : 1.0f);
      centerA[axis] += sign * (overlap[axis] + eps);
      
      return centerA;
    }
  } // namespace CollisionMath
} // namespace IronFrost


#endif

#include "widget.hpp"

// C++ standard library
#include <iostream>
#include <string>

namespace IronFrost {
  Widget::Widget(const glm::vec2& position, const glm::vec2& size) :
    Transformable(position, size, 0.0F)
  {}

  void Widget::update(float deltaTime, GameContext& gameContext) {
    if (!isVisible()) {
      return;
    }

    const auto& position = getPosition();
    const auto& size = getSize();

    if (gameContext.mouse.getXPos() > position.x && gameContext.mouse.getXPos() < position.x + size.x &&
        gameContext.mouse.getYPos() > position.y && gameContext.mouse.getYPos() < position.y + size.y) {

      if (!isHovered()) {
        triggerCallback("onHover");
        m_hovered = true;
      }

      if (gameContext.mouse.isButtonPressed(MOUSE_BUTTON_LEFT)) {
        if (!isPressed()) {
          triggerCallback("onClick");
          m_pressed = true;
        }
      } else {
        m_pressed = false;
      }
    } else {
      m_hovered = false;
    }
  }

  bool Widget::isVisible() const {
    return m_visible;
  }

  void Widget::setVisible(bool visible) {
    m_visible = visible;
  }

  bool Widget::isHovered() const {
    return m_hovered;
  }

  bool Widget::isFocused() const {
    return m_focused;
  }

  bool Widget::isPressed() const
  {
    return m_pressed;
  }

  std::string Widget::getType() const {
    return "widget";
  }
}

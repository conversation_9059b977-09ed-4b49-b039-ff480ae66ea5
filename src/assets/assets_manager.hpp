#ifndef __IF__ASSETS_HPP
#define __IF__ASSETS_HPP

// C++ standard library
#include <memory>

// Third-party libraries
#include <glm/glm.hpp>

// Local includes
#include "../utils/string_id.hpp"
#include "../utils/containers_set.hpp"
#include "asset_data_types.hpp"
#include "asset_primitive_types.hpp"

namespace IronFrost {
  class IVFS;
  
  class AssetsManager {
    private:
      IVFS& m_vfs;

      PointerContainersSet<
        ShaderData, 
        PostprocessData, 
        ImageData, 
        HeightmapData, 
        FontData, 
        MaterialData, 
        MeshData, 
        ModelData, 
        TerrainData, 
        AudioData
      > m_assetsContainers;

    public:
      explicit AssetsManager(IVFS& vfs);
      AssetsManager(const AssetsManager &) = delete;

      template<typename T>
      void insert(const StringID& name, std::unique_ptr<T> element) {
        m_assetsContainers.getContainer<T>().insert(name, std::move(element));
      }

      template<typename T>
      const T& get(const StringID& name) const {
        return m_assetsContainers.getContainer<T>().get(name);
      }

      template<typename T>
      bool has(const StringID& name) const {
        return m_assetsContainers.getContainer<T>().has(name);
      }

      template<typename T>
      void unload(const StringID& name) {
        m_assetsContainers.getContainer<T>().remove(name);
      }

      template<typename T>
      void clear() {
        m_assetsContainers.getContainer<T>().clear();
      }

      template<typename T>
      std::optional<StringID> findID(const T* ptr) const {
        return m_assetsContainers.getContainer<T>().findID(ptr);
      }

      std::unique_ptr<ShaderData> loadShader(const std::string& path);
      const ShaderData& loadShader(const StringID& name, const std::string& path);

      std::unique_ptr<MeshData> createPrimitive(PrimitiveType type, const PrimitiveParams& params = {});
      const MeshData& createPrimitive(const StringID& name, PrimitiveType type, const PrimitiveParams& params = {});

      std::unique_ptr<ModelData> loadModel(const std::string& path);
      const ModelData& loadModel(const StringID& name, const std::string& path);

      std::unique_ptr<ImageData> loadImage(const std::string& path);
      const ImageData& loadImage(const StringID& name, const std::string& path);

      std::unique_ptr<HeightmapData> loadHeightmap(const std::string& path);
      const HeightmapData& loadHeightmap(const StringID& name, const std::string& path);

      std::unique_ptr<TerrainData> loadTerrain(const TerrainParams& params);
      const TerrainData& loadTerrain(const StringID& name, const TerrainParams& params);

      std::unique_ptr<AudioData> loadAudio(const std::string& path);
      const AudioData& loadAudio(const StringID& name, const std::string& path);

      std::unique_ptr<FontData> loadFont(const std::string& path, unsigned int size);
      const FontData& loadFont(const StringID& name, const std::string& path, unsigned int size);

      const MaterialData& storeMaterial(const StringID& name, std::unique_ptr<MaterialData> materialData);
    };
}

#endif

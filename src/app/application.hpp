#ifndef __IF__APPLICATION_HPP
#define __IF__APPLICATION_HPP

// C++ standard library
#include <memory>

// Local includes
#include "../events/event_dispatcher.hpp"
#include "../utils/delta_time_calculator.hpp"

namespace IronFrost {
  class ApplicationBuilder;
  class AssetsManager;
  class Console;
  class ConsoleManager;
  struct GameContext;
  class GUI;
  class IAudioEngine;
  class IRenderer;
  class IScriptEngine;
  class IVFS;
  class IWindow;
  class SceneManager;

  class Application {
    friend class ApplicationBuilder;

    private:
      DeltaTimeCalculator m_deltaTimeCalculator;

      std::unique_ptr<IWindow> m_window;
      std::unique_ptr<IRenderer> m_renderer;
      std::unique_ptr<IVFS> m_vfs;
      std::unique_ptr<IAudioEngine> m_audioEngine;
      std::unique_ptr<AssetsManager> m_assetsManager;
      std::unique_ptr<GameContext> m_gameContext;
      std::unique_ptr<Console> m_console;
      std::unique_ptr<GUI> m_globalGUI;
      std::unique_ptr<ConsoleManager> m_consoleManager;
      std::unique_ptr<SceneManager> m_sceneManager;
      std::unique_ptr<IScriptEngine> m_scriptEngine;  // Must be destroyed after SceneManager
    public:
      Application();
      ~Application(); // Implemented in .cpp to handle incomplete types

      void run();

      IWindow& getWindow();
      IVFS& getVFS();
      Console& getConsole();
      IAudioEngine& getAudioEngine();
  };
}

#endif

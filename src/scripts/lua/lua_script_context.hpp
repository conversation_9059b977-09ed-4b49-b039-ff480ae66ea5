#ifndef __IF__LUA_SCRIPT_CONTEXT_HPP
#define __IF__LUA_SCRIPT_CONTEXT_HPP

// C++ standard library
#include <memory>
#include <string>
#include <vector>

// Third-party libraries
#include <sol/sol.hpp>

// Local includes
#include "../script_context.hpp"

namespace IronFrost {
  class Application;
  class LuaAPIBinder;

  /**
   * Base class for Lua script contexts.
   * Provides common functionality for managing Lua states, compiling scripts,
   * and executing functions. Can be specialized for different contexts
   * (global, scene, etc.).
   */
  class LuaScriptContext : public virtual IScriptContext {
    protected:
      Application& m_application;
      LuaAPIBinder* m_apiBinder;
      sol::state m_luaState;
      std::vector<sol::function> m_perFrameFunctions;

    public:
      LuaScriptContext(Application& application, LuaAPIBinder& apiBinder);
      virtual ~LuaScriptContext() = default;

      // Non-copyable and non-movable (due to reference members)
      LuaScriptContext(const LuaScriptContext&) = delete;
      LuaScriptContext& operator=(const LuaScriptContext&) = delete;
      LuaScriptContext(LuaScriptContext&&) = delete;
      LuaScriptContext& operator=(LuaScriptContext&&) = delete;

      /**
       * Initialize the Lua context with APIs and context-specific setup.
       */
      void initialize() override;

      /**
       * Execute initialization scripts (run once).
       */
      void executeInitScripts(const std::vector<std::string>& scriptContents) override;

      /**
       * Register per-frame scripts (compile to functions).
       */
      void registerPerFrameScripts(const std::vector<std::string>& scriptContents) override;

      /**
       * Execute per-frame scripts with given deltaTime.
       */
      void executePerFrame(float deltaTime) override;

      /**
       * Get access to the underlying Lua state for advanced operations.
       */
      sol::state& getLuaState() { return m_luaState; }

      /**
       * Clear all Lua function references to avoid destruction order issues.
       */
      void clearLuaReferences();

    protected:
      /**
       * Set up context-specific variables and bindings.
       * Override in derived classes for context-specific setup.
       */
      virtual void setupContextVariables() = 0;

      /**
       * Get the print prefix for this context (e.g., "[Scene:name]").
       */
      virtual std::string getPrintPrefix() const = 0;

    private:
      void initializeLuaState();
      void bindAPIs();
  };
}

#endif

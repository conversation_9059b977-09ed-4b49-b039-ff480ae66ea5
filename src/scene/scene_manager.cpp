#include "scene_manager.hpp"

// C++ standard library
#include <future>
#include <iostream>
#include <memory>
#include <string>

// Local includes
#include "../assets/assets_manager.hpp"
#include "../events/event_dispatcher.hpp"
#include "../renderer/renderer.hpp"
#include "../scripts/script_context.hpp"
#include "../scripts/script_engine.hpp"
#include "../utils/game_context.hpp"
#include "../vfs/vfs.hpp"
#include "scene_context.hpp"
#include "scene_data_storage.hpp"

namespace IronFrost {
  SceneManager::SceneManager(IVFS& vfs, AssetsManager& assetsManager, IRenderer& renderer, IScriptEngine* scriptEngine) {
    m_sceneDataStorage = std::make_unique<SceneDataStorage>();
    m_sceneContext = std::make_unique<SceneContext>(assetsManager, *m_sceneDataStorage, vfs, renderer, scriptEngine);
    // m_loadingScene = std::make_unique<LoadingScene>("", *m_sceneContext);
  }

  SceneManager::~SceneManager() {
    if (m_currentScene != nullptr) {
      m_currentScene->unload();
    }
  }

  void SceneManager::loadScenesFromFile(const std::string& path) {
    json scenesConfig = json::parse(m_sceneContext->vfs.readFile(path));

    for (json::iterator it = scenesConfig.begin(); it != scenesConfig.end(); ++it) {
      auto scene = it.value();
      createScene(StringID(scene["name"]), scene["path"]);
    }
  }

  void SceneManager::createScene(const StringID& sceneName, const std::string& scenePath) {
    m_scenes.emplace(sceneName, std::make_unique<GameScene>(sceneName, scenePath, *m_sceneContext));
  }

  void SceneManager::switchToScene(const StringID& sceneName) {
    auto it = m_scenes.find(sceneName);
    if (it != m_scenes.end()) {
      std::cout << "Switching to scene: " << StringID::getString(sceneName) << '\n';

      if (m_currentScene != nullptr) {
        m_currentScene->unload();
      }

      m_currentScene = it->second.get();
      m_currentScene->initialize();
    }
  }

  void SceneManager::updateCurrentScene(float deltaTime, GameContext& gameContext) {
    if (m_currentScene->isLoaded()) {
      m_currentScene->update(deltaTime, gameContext);
    } else {
      m_currentScene->load();
      // m_loadingScene->update(deltaTime, gameContext);
    }
  }

  void SceneManager::renderCurrentScene() const {
    if (m_currentScene->isLoaded()) {
      m_currentScene->render();
    } else {
      // m_loadingScene->render();
    }
  }

  void SceneManager::executeScenePerFrameScripts(float deltaTime) {
    if (m_currentScene && m_currentScene->isLoaded()) {
      // Cast to GameScene to access script execution
      auto* gameScene = static_cast<GameScene*>(m_currentScene);
      gameScene->executePerFrameScripts(deltaTime);
    }
  }

  StringID SceneManager::getCurrentSceneName() const {
    if (m_currentScene) {
      return m_currentScene->getSceneName();
    }
    return StringID("");
  }
}

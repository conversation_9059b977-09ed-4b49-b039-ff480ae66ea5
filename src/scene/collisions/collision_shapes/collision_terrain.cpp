#include "collision_terrain.hpp"
#include "collision_sphere.hpp"
#include "collision_aabb.hpp"
#include "collision_plane.hpp"

#include <iostream>

namespace IronFrost {
  std::optional<float> CollisionTerrain::getTerrainHeight(float worldX, float worldZ) const {
    float x = (worldX - m_origin.x) / (m_blockSize * m_scaleX);
    float z = (worldZ - m_origin.z) / (m_blockSize * m_scaleZ);

    if (x < 0 || x >= m_heightmapData->width || z < 0 || z >= m_heightmapData->height) {
      std::cout << "Out of bounds: " << x << ", " << z << std::endl;
      return std::nullopt;
    }

    float h = m_heightmapData->getInterpolatedHeight(x, z);
    float worldY = m_origin.y + h * m_heightScale * m_scaleY;

    return worldY;
  }

  void CollisionTerrain::update(const glm::mat4& transform) {
    m_transform = transform;

    m_transformNormal = glm::inverseTranspose(glm::mat3(transform));

    m_scaleX = glm::length(glm::vec3(transform[0]));
    m_scaleY = glm::length(glm::vec3(transform[1]));
    m_scaleZ = glm::length(glm::vec3(transform[2]));
    m_origin = glm::vec3(transform[3]);

    m_worldAABB = CollisionMath::transformAABB(m_localAABB, transform);
  }

  CollisionMath::AABB CollisionTerrain::getWorldAABB() const {
    return m_worldAABB;
  }

  bool CollisionTerrain::intersects(const CollisionShape& other) const {
    return other.intersectsTerrain(*this);
  }

  bool CollisionTerrain::intersectsSphere(const CollisionSphere& sphere) const {
    return CollisionMath::sphereAABBIntersection(sphere.getWorldSphere(), m_worldAABB);
  }

  bool CollisionTerrain::intersectsAABB(const CollisionAABB& aabb) const {
    return CollisionMath::aabbIntersection(aabb.getWorldAABB(), m_worldAABB);
  }

  bool CollisionTerrain::intersectsPlane(const CollisionPlane& plane) const {
    return CollisionMath::aabbPlaneIntersection(m_worldAABB, plane.getWorldPlane());
  }

  bool CollisionTerrain::intersectsTerrain(const CollisionTerrain& terrain) const {
    return CollisionMath::aabbIntersection(m_worldAABB, terrain.getWorldAABB());
  }

  glm::vec3 CollisionTerrain::resolveSphereCollision(const CollisionMath::Sphere& sphere) {
    auto h = getTerrainHeight(sphere.center.x, sphere.center.z);

    if (!h.has_value()) {
      return sphere.center;
    }

    float bottomY = sphere.center.y - sphere.radius;

    std::cout << "Terrain height: " << *h << std::endl;
    
    if (bottomY < *h) {
      return glm::vec3(sphere.center.x, *h + sphere.radius, sphere.center.z);
    }

    return sphere.center;
  }

  glm::vec3 CollisionTerrain::resolveAABBCollision(const CollisionMath::AABB& aabb) {
    // No-op
    return aabb.getCenter();
  }
}

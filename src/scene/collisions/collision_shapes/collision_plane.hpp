#ifndef __IF__COLLISION_PLANE_HPP
#define __IF__COLLISION_PLANE_HPP

// Local includes
#include "collision_shape.hpp"

namespace IronFrost {
  class CollisionPlane : public CollisionShape {
    private:
      CollisionMath::Plane m_localPlane;
      CollisionMath::Plane m_worldPlane;

    public:
      CollisionPlane(const CollisionMath::Plane& plane) : 
        m_localPlane(plane), m_worldPlane(plane) {}

      CollisionPlane(const CollisionMath::Plane& plane, const glm::mat4& transform) :
        m_localPlane(plane), m_worldPlane(CollisionMath::transformPlane(plane, transform)) {}

      ~CollisionPlane() override = default;

      void update(const glm::mat4& transform) override;

      CollisionMath::Plane getWorldPlane() const;
      CollisionMath::AABB getWorldAABB() const override;
      
      bool intersects(const CollisionShape& other) const override;
      bool intersectsSphere(const CollisionSphere& sphere) const override;
      bool intersectsAABB(const CollisionAABB& aabb) const override;
      bool intersectsPlane(const CollisionPlane& plane) const override;
      bool intersectsTerrain(const CollisionTerrain& terrain) const override;

      glm::vec3 resolveSphereCollision(const CollisionMath::Sphere& sphere) override;      
      glm::vec3 resolveAABBCollision(const CollisionMath::AABB& aabb) override;
  };
}

#endif

#ifndef __IF__GUI_LOADER_HPP
#define __IF__GUI_LOADER_HPP

// C++ standard library
#include <memory>

// Third-party libraries
#include <nlohmann/json.hpp>

// Local includes
#include "../../gui/widgets/image_widget.hpp"
#include "../../gui/widgets/label_widget.hpp"

using json = nlohmann::json;

namespace IronFrost {
  class GUILoader {
    public:
      std::make_unique<ImageWidget> loadImageWidget(json ) {
        
      }
  }
}

#endif

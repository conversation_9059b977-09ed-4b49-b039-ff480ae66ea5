#include "gui_renderer.hpp"

// C++ standard library
#include <algorithm>
#include <iostream>
#include <string>
#include <tuple>
#include <vector>

// Third-party libraries
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>

// Local includes
#include "../events/event_dispatcher.hpp"
#include "../gui/gui.hpp"
#include "../gui/widgets/image_widget.hpp"
#include "../gui/widgets/label_widget.hpp"
#include "../gui/widgets/panel_widget.hpp"
#include "../services/service_locator.hpp"
#include "../window/window.hpp"
#include "renderer.hpp"
#include "resource_manager.hpp"

namespace IronFrost {
  void GUIRenderer::updateProjectionMatrix() {
    const auto& window = m_renderer.getWindow();

    float screenWidth = window.getWidth();  
    float screenHeight = window.getHeight();

    float scale = std::min(screenWidth / m_baseScreenWidth, screenHeight / m_baseScreenHeight);

    float effectiveWidth = screenWidth / scale; 
    float effectiveHeight = screenHeight / scale;

    float offsetX = (m_baseScreenWidth - effectiveWidth) / 2.0F;
    float offsetY = (m_baseScreenHeight - effectiveHeight) / 2.0F;

    m_projectionMatrix = glm::ortho(offsetX, offsetX + effectiveWidth, offsetY + effectiveHeight, offsetY, -1.0F, 1.0F);
  }

  RenderableObject GUIRenderer::createRenderableImage(const ImageWidget& widget) {
    const IResourceManager& resourceManager = m_renderer.getResourceManager();
    const GUIMaterial material{resourceManager.get<TextureHandle>(StringID(widget.getTextureName()))};

    return {
      .shaderHandle = m_shader,
      .meshHandle = m_quadMesh,
      .material = material,
      .uniforms = {
        {"color", widget.getColor()},
        {"alpha", widget.getAlpha()}
      }
    };
  }

  /*static*/ glm::mat4 GUIRenderer::computeGlyphTransform(const GlyphHandle& glyph, const glm::vec2& position, const glm::vec2& scale, unsigned int lineHeight) {
    glm::vec2 glyphSize = { glyph.size.x * scale.x, glyph.size.y * scale.y };
    glm::vec2 glyphPosition = {
      position.x + (glyphSize.x / 2) + (glyph.bearing.x * scale.x),
      position.y + (glyphSize.y / 2) + ((static_cast<float>(lineHeight) - glyph.size.y) * scale.y) + ((glyph.size.y - glyph.bearing.y) * scale.y)
    };

    auto transform = glm::mat4(1.0F);
    transform = glm::translate(transform, glm::vec3(glyphPosition, 0.0F));
    transform = glm::scale(transform, glm::vec3(glyphSize, 1.0F));

    return transform;
  }

  RenderableObject GUIRenderer::createRenderableGlyph(const LabelWidget& widget, const GlyphHandle& glyph) {
    const TextureHandle textureHandle{.textureID = glyph.textureID};
    const GUIMaterial material{textureHandle};

    return RenderableObject{
      .shaderHandle = m_renderer.getResourceManager().get<ShaderHandle>(DefaultShaders::DEFAULT_TEXT_SHADER_NAME),
      .meshHandle = m_quadMesh,
      .material = material,
      .uniforms = {
        {"color", widget.getColor()},
        {"alpha", widget.getAlpha()}
      }
    };
  }

  std::vector<std::tuple<RenderableObject, glm::mat4>> GUIRenderer::createRenderableLabel(const LabelWidget& widget) {
    StringID fontName = widget.getFontName();
    const std::string& text = widget.getText();
    const FontHandle& fontHandle = m_renderer.getResourceManager().get<FontHandle>(fontName);

    std::vector<std::tuple<RenderableObject, glm::mat4>> renderables;
    renderables.reserve(text.size());

    glm::vec2 position = widget.getPosition();
    const glm::vec2 scale = widget.getSize();

    for(const char& c : text) {
      if(c == '\n') {
        position.x = widget.getPosition().x;
        position.y += static_cast<float>(fontHandle.lineHeight) * scale.y;
        continue;
      }

      const GlyphHandle& glyphHandle = fontHandle.glyphs[c];

      glm::mat4 transform = computeGlyphTransform(glyphHandle, position, scale, static_cast<unsigned int>(fontHandle.lineHeight));
      renderables.emplace_back(createRenderableGlyph(widget, glyphHandle), transform);

      position.x += static_cast<float>(glyphHandle.advance) * scale.x;
    }

    return renderables;
  }

  void GUIRenderer::submitWidget(Widget& widget) {
    if(auto* panel = dynamic_cast<PanelWidget*>(&widget)) {
      panel->traverseWidgets([&](Widget& childWidget) {
        if (childWidget.isVisible()) {
          submitWidget(childWidget);
          return;
        }
      });
    }

    if (widget.isDirty()) {
      m_renderableCache[&widget].clear();
      createAndCacheRenderables(widget);
      widget.markClean();
    }

    auto it = m_renderableCache.find(&widget);
    if (it != m_renderableCache.end()) {
      for (auto& [renderableObject, transform] : it->second) {
        m_renderer.submitRenderableObject(renderableObject, InstanceData{.transform = transform});
      }
    }
  }

  void GUIRenderer::createAndCacheRenderables(Widget& widget) {
    auto widgetType = widget.getType();

    if (widgetType == "image") {
      processImageWidget(dynamic_cast<ImageWidget&>(widget));
    } else if (widgetType == "label") {
      processLabelWidget(dynamic_cast<LabelWidget&>(widget));
    } else if (widgetType == "panel") {
      processPanelWidget(dynamic_cast<PanelWidget&>(widget));
    }
  }

  void GUIRenderer::cacheAndSubmitRenderable(Widget& widget, const RenderableObject& renderableObject, const glm::mat4& transform) {
    m_renderableCache[&widget].emplace_back(renderableObject, transform);
    m_renderer.submitRenderableObject(renderableObject, InstanceData{.transform = transform});
  }

  void GUIRenderer::processImageWidget(ImageWidget& imageWidget) {
    auto renderableObject = createRenderableImage(imageWidget);

    cacheAndSubmitRenderable(imageWidget, renderableObject, imageWidget.getTransform());
  }

  void GUIRenderer::processLabelWidget(LabelWidget& labelWidget) {
    auto renderableObjects = createRenderableLabel(labelWidget);

    for (auto& [renderableObject, transform] : renderableObjects) {
      cacheAndSubmitRenderable(labelWidget, renderableObject, transform);
    }
  }

  void GUIRenderer::processPanelWidget(PanelWidget& panelWidget) {
    panelWidget.traverseWidgets([&](Widget& childWidget) {
        createAndCacheRenderables(childWidget);
    });
  }

  GUIRenderer::GUIRenderer(IRenderer& renderer) :
    m_renderer(renderer),
    m_quadMesh(renderer.getResourceManager().get<MeshHandle>(FallbackResources::DEFAULT_QUAD_NAME)),
    m_shader(renderer.getResourceManager().get<ShaderHandle>(DefaultShaders::DEFAULT_GUI_SHADER_NAME)) {
    updateProjectionMatrix();

    ServiceLocator::getService<EventDispatcher>().registerListener<WindowResizeEvent>(
      [&](const WindowResizeEvent &event) {
        updateProjectionMatrix();
      });
  }

  float GUIRenderer::getBaseScreenWidth() const {
    return m_baseScreenWidth;
  }

  float GUIRenderer::getBaseScreenHeight() const {
    return m_baseScreenHeight;
  }

  void GUIRenderer::render(GUI& gui) {
    m_renderer.clearRenderQueue();
    m_renderer.setViewProjection(glm::mat4(1.0F), m_projectionMatrix);

    gui.traverseWidgets(
      [&](Widget& widget) {
        if (!widget.isVisible()) {
          return;
        }

        submitWidget(widget);
      });

    m_renderer.withRenderState({.depthTest = false, .cullFace = false, .blend = true}, [&]() {
      m_renderer.render();
    });
  }
}

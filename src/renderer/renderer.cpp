#include "gui_renderer.hpp"
#include "opengl/opengl_renderer.hpp"
#include "renderer.hpp"

// C++ standard library
#include <algorithm>
#include <exception>
#include <iterator>
#include <memory>
#include <stdexcept>
#include <string>
#include <utility>
#include <vector>

// Third-party libraries
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>

// Local includes
#include "../assets/assets_manager.hpp"
#include "../events/event_dispatcher.hpp"
#include "../window/window.hpp"
#include "postprocess_effect.hpp"
#include "renderables_manager.hpp"
#include "resource_manager.hpp"
#include "postprocess_manager.hpp"

namespace IronFrost {
  IRenderer::IRenderer(IWindow& window) :
    m_window(window)
  {}

  IRenderer::~IRenderer() = default;

  const IWindow& IRenderer::getWindow() {
    return m_window;
  }

  GUIRenderer& IRenderer::getGUIRenderer() {
    if(!m_guiRenderer) {
      m_guiRenderer = std::make_unique<GUIRenderer>(*this);
    }

    return *m_guiRenderer;
  }

  PostprocessEffect& IRenderer::createPostprocessEffect(const StringID& name, const std::vector<std::string>& shaderNames) {
    return m_postprocessManager->createPostprocessEffect(name, shaderNames);
  }

  PostprocessEffect& IRenderer::getPostprocessEffect(const StringID& name) {
    return m_postprocessManager->getPostprocessEffect(name);
  }

  void IRenderer::setViewProjection(const glm::mat4& view, const glm::mat4& projection) {
    m_viewMatrix = view;
    m_projectionMatrix = projection;
  }

  void IRenderer::setView(const glm::mat4& view) {
    m_viewMatrix = view;
  }

  void IRenderer::setProjection(const glm::mat4& projection) {
    m_projectionMatrix = projection;
  }

  void IRenderer::submitRenderableObject(const RenderableObject& renderableObject, const InstanceData& instanceData) {
    m_rendererQueue.submit(std::make_tuple(renderableObject, instanceData));
  }

  void IRenderer::submitRenderableObject(RenderableObjectID renderableObjectID, const InstanceData& instanceData) {
    auto* rendenderableObject = m_renderablesManager->getRenderableObject(renderableObjectID);

    if(rendenderableObject != nullptr) {
      submitRenderableObject(*rendenderableObject, instanceData);
    }
  }

  void IRenderer::submitRenderableLight(const RenderableLight& renderableLight) {
    m_renderableLights.push_back(renderableLight);
  }

  void IRenderer::clearRenderQueue() {
    m_rendererQueue.clear();
    m_renderableLights.clear();
  }

  std::unique_ptr<IRenderer> IRenderer::create(RENDERER_TYPE rendererType, IWindow& window) {
    if (rendererType == RENDERER_TYPE::OPENGL) {
      return OpenGLRenderer::create(window);
    }

    throw std::runtime_error("Renderer type not supported");
  }
}

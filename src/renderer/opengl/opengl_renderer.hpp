#ifndef __IF__OPENGL_RENDERER_HPP
#define __IF__OPENGL_RENDERER_HPP

// C++ standard library
#include <functional>
#include <memory>
#include <string>

// Third-party libraries
#include <glad/glad.h>

// Local includes
#include "../renderer.hpp"

namespace IronFrost {
  class OpenGLScreen;
  
  class OpenGLRenderer : public IRenderer {
  private:
    unsigned int m_currentShader{0};
    int m_maxTextureUnits{0};

    std::unique_ptr<OpenGLScreen> m_screen{nullptr};

    bool hasUniform(const ShaderHandle& shaderHandle, const std::string& name);
    int getUniformLocation(const ShaderHandle& shaderHandle, const std::string& name);
    void setUniform(const ShaderHandle& shaderHandle, const std::string& name, const UniformValue& value);
    void setUniforms(const ShaderHandle& shaderHandle, const ShaderUniforms& shaderUniforms);

    void bindBlinnPhongMaterial(const ShaderHandle& shaderHandle, const BlinnPhongMaterial& material);
    void bindPBRMaterial(const ShaderHandle& shaderHandle, const PBRMaterial& material);
    void bindMaterial(const ShaderHandle& shaderHandle, const Material& material);

    void createDefaultShaders();

    void setRenderTargetDefault();
    void setRenderToFramebuffer(const FramebufferHandle& framebufferHandle);
    void renderFramebufferToScreen(const FramebufferHandle& framebufferHandle);

    void setPointLightUniforms(const ShaderHandle& shaderHandle);

    glm::mat3 calculateNormalMatrix(const glm::mat4& modelMatrix);

    OpenGLRenderer(IWindow& window);
  public:
    ~OpenGLRenderer();

    static std::unique_ptr<OpenGLRenderer> create(IWindow& window);

    void useShader(unsigned int id) override;
    void unbindShader() override;

    void beginFrame() override;
    void endFrame() override;
    void render() override;
    void renderToFramebuffer(std::function<void()> callback) override;
    void renderPostprocessEffect(const PostprocessEffect& postprocessEffect) override;

    void withRenderState(const RenderState& state, std::function<void()> func) override;
  };
}

#endif

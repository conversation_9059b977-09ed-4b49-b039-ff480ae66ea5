#ifndef __IF__GUI_RENDERER_HPP
#define __IF__GUI_RENDERER_HPP

// C++ standard library
#include <unordered_map>
#include <vector>

// Local includes
#include "gpu_handles.hpp"
#include "renderables/renderable_object.hpp"

namespace IronFrost {
  class EventDispatcher;
  class GUI;
  class IRenderer;
  class ImageWidget;
  class LabelWidget;
  class PanelWidget;
  class Widget;

  class GUIRenderer {
    private:
      IRenderer& m_renderer;

      const ShaderHandle& m_shader;
      const MeshHandle& m_quadMesh;
      glm::mat4 m_projectionMatrix;

      float m_baseScreenWidth{3840.0};
      float m_baseScreenHeight{2160.0};

      std::unordered_map<Widget*, std::vector<std::tuple<RenderableObject, glm::mat4>>> m_renderableCache;

      void submitWidget(Widget& widget);

      void createAndCacheRenderables(Widget& widget);
      void cacheAndSubmitRenderable(Widget& widget, const RenderableObject& renderableObject, const glm::mat4& transform);

      void processImageWidget(ImageWidget& widget);
      void processLabelWidget(LabelWidget& widget);
      void processPanelWidget(PanelWidget& widget);

      void updateProjectionMatrix();

      static glm::mat4 computeGlyphTransform(const GlyphHandle& glyph, const glm::vec2& position, const glm::vec2& scale, unsigned int lineHeight);
      RenderableObject createRenderableImage(const ImageWidget& widget);
      RenderableObject createRenderableGlyph(const LabelWidget& widget, const GlyphHandle& glyph);
      std::vector<std::tuple<RenderableObject, glm::mat4>> createRenderableLabel(const LabelWidget& widget);
    public:
      GUIRenderer(IRenderer& renderer);

      GUIRenderer& operator=(const GUIRenderer&) = delete;
      GUIRenderer(const GUIRenderer&) = delete;

      float getBaseScreenWidth() const;
      float getBaseScreenHeight() const;

      void render(GUI& gui);
  };
}

#endif

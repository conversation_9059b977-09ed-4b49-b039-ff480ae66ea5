#ifndef __IF__COLOR_HPP
#define __IF__COLOR_HPP

namespace IronFrost {
  class Color {
    private:
      glm::vec4 m_color;

    public:
      Color(float r, float g, float b, float a = 1.0f) {
        m_color = glm::vec4(r, g, b, a);
      }

      Color(glm::vec4 color) {
        m_color = color;
      }

      Color(glm::vec3 color) {
        m_color = glm::vec4(color, 1.0f);
      }

      const glm::vec4& getColor() const {
        return m_color;
      }

      float getR() const {
        return m_color.r;
      }

      float getG() const {
        return m_color.g;
      }

      float getB() const {
        return m_color.b;
      }

      float getA() const {
        return m_color.a;
      }
  }
}

#endif

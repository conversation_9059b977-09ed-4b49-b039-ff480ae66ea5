-- GUI Interactions Module
-- Handles scene-specific GUI widget interactions

local GUIInteractions = {}

-- Initialize GUI interactions
function GUIInteractions.init()
  -- GUI interactions ready
end

-- Update GUI interactions
function GUIInteractions.update(scene)
  if not scene then
    return
  end

  local gui = scene:getG<PERSON>()
  if not gui then
    return
  end

  -- Set up widget interactions
  GUIInteractions.setupImageWidget(gui)
end

-- Setup image widget interactions
function GUIInteractions.setupImageWidget(gui)
  local label = gui:getWidget(StringID.new("label"))
  local image = gui:getWidget(StringID.new("image"))

  if image then
    image:setCallback("onClick", function()
      print("[GUIInteractions] Image clicked!")
      if label then
        label:setText("Image clicked!")
      end
    end)
  end
end

return GUIInteractions
